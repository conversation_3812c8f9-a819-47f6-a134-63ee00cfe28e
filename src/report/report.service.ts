import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { Fill, Workbook } from 'exceljs';
import { CreateReportDto } from './dto/create-report.dto';
import { UpdateReportDto } from './dto/update-report.dto';
import { OrderService } from 'src/order/order.service';
import { DateTime } from 'luxon';
import { Branch } from 'src/branch/entities/branch.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, DataSource, In, Repository } from 'typeorm';
import { Shift } from 'src/shift/entities/shift.entity';
import { Order, OrderStatus } from 'src/order/entities/order.entity';
import {
  chain,
  groupBy,
  map,
  sumBy,
} from 'lodash';
import { OrderItem } from 'src/order/entities/order-item.entity';
import { OrderPaymentStatus } from 'src/order/entities/order-payment.entity';
import { Category } from 'src/category/entities/category.entity';
import { Product } from 'src/product/entities/product.entity';
import * as ExcelJS from 'exceljs';
import { Payment, PaymentType } from 'src/payment/entities/payment.entity';
import { FoodType, Member } from '../member/entities/member.entity';
import { datetime2string } from 'src/common/DatetimeUtil';
import { User } from 'src/user/entities/user.entity';
import { Device } from 'src/device/entities/device.entity';
import { PaymentMethodService } from 'src/payment-method/payment-method.service';
import { Buffer } from 'buffer';
import { AuditLog } from 'src/auditlog/entities/auditlog.entity';

const borderStyle: Partial<ExcelJS.Borders> = {
  top: { style: 'thin' as ExcelJS.BorderStyle },
  left: { style: 'thin' as ExcelJS.BorderStyle },
  bottom: { style: 'thin' as ExcelJS.BorderStyle },
  right: { style: 'thin' as ExcelJS.BorderStyle },
};
@Injectable()
export class ReportService {
  exportBuffet(start: Date, end: Date, foodType: FoodType): any {
    throw new Error('Method not implemented.');
  }
  constructor(
    private dataSource: DataSource,
    private orderService: OrderService,
    private payMethodService: PaymentMethodService,
    @InjectRepository(Branch) private branchRepository: Repository<Branch>,
    @InjectRepository(Shift) private shiftRepository: Repository<Shift>,
    @InjectRepository(Order) private orderRepository: Repository<Order>,
    @InjectRepository(Product) private productRepository: Repository<Product>,
    @InjectRepository(OrderItem)
    private orderItemRepository: Repository<OrderItem>,
  ) {}

  // async reportOrder(startDate: Date, endDate: Date) {
  //   const workbook = new Workbook();

  //   const worksheet = workbook.addWorksheet('order');

  //   worksheet.columns = [
  //     { header: '', key: 'no' },
  //     { header: 'No', key: 'order_no', width: 16 },
  //     { header: 'Date', key: 'order_date', width: 12 },
  //     { header: 'Time', key: 'order_time', width: 12 },
  //     { header: 'Status', key: 'order_status', width: 17 },
  //     { header: 'Discount', key: 'discount' },
  //     { header: 'Total', key: 'grand_total' },
  //     { header: 'Branch', key: 'branch', width: 14 },
  //     { header: 'Cashier', key: 'cashier', width: 14 },
  //   ];

  //   const content = await this.orderService.report(startDate, endDate);
  //   // console.log(content)
  //   const data = content.map((e, i, _) => {
  //     const date = DateTime.fromJSDate(e.orderDate).setLocale('th-TH');

  //     return {
  //       no: i + 1,
  //       order_no: e.orderNo,
  //       order_date: date.toLocaleString({
  //         year: 'numeric',
  //         month: '2-digit',
  //         day: '2-digit',
  //       }),
  //       order_time: date.toLocaleString(DateTime.TIME_SIMPLE),
  //       order_status: e.convertStatusToThai(),
  //       discount: e.discount,
  //       grand_total: e.grandTotal,
  //       branch: e?.shift?.branch?.name ?? '-',
  //       cashier: e?.shift?.user?.fullName ?? '-',
  //     };
  //   });

  //   data.forEach((val, i, _) => {
  //     // console.log(val);
  //     worksheet.addRow(val);
  //   });

  //   worksheet.eachRow({ includeEmpty: true }, (row, rowNumber) => {
  //     if (rowNumber == 1) {
  //       row.eachCell((cell, cellNumber) => {
  //         cell.font = {
  //           size: 14,
  //           bold: true,
  //         };
  //         cell.border = {
  //           top: { style: 'thin' },
  //           left: { style: 'thin' },
  //           bottom: { style: 'thin' },
  //           right: { style: 'thin' },
  //         };
  //       });
  //     }
  //   });

  //   // const buffer = await workbook.xlsx.writeBuffer()
  //   return workbook.xlsx.writeBuffer();
  // }

  // async reportOrderCashier(startDate: Date, endDate: Date) {
  //   const workbook = new Workbook();

  //   const worksheet = workbook.addWorksheet('order');

  //   worksheet.columns = [
  //     { header: 'วันที่', key: 'date', width: 12 },
  //     { header: 'รหัสสาขา', key: 'branch_code', width: 12 },
  //     { header: 'ชื่อสาขา', key: 'branch_name', width: 12 },
  //     { header: 'เงินสด', key: 'cash', width: 12 },
  //     { header: 'QR', key: 'qr', width: 12 },
  //     { header: 'แม่มณี', key: 'mae_manee', width: 12 },
  //     { header: 'ยอดเงินรวม', key: 'total', width: 14 },
  //   ];

  //   // const orders = await this.orderRepository
  //   //     .createQueryBuilder('order')
  //   //     .where('order.orderStatus = :orderStatus', { orderStatus: OrderStatus.COMPLETE })
  //   //     .andWhere('order.orderDate BETWEEN :startDate AND :endDate', { startDate: startDate, endDate: endDate })
  //   //     .leftJoinAndSelect('order.orderPayments',  'orderPayment')
  //   //     .leftJoinAndSelect('orderPayment.paymentMethod',  'paymentMethod')
  //   //     .leftJoinAndSelect('order.shift',  'shift')
  //   //     .leftJoinAndSelect('shift.branch', 'branch')
  //   //     .getMany()

  //   const orders = await this.orderRepository.find({
  //     where: {
  //       shift: {
  //         date: Between(startDate, endDate),
  //       },
  //       orderStatus: OrderStatus.COMPLETE,
  //     },
  //     relations: {
  //       shift: {
  //         branch: true,
  //       },
  //       orderPayments: {
  //         paymentMethod: true,
  //       },
  //     },
  //   });

  //   // const result = await this.shiftRepository.find({
  //   //     where: {
  //   //         date: Between(startDate, endDate)
  //   //     },
  //   //     relations: {
  //   //         branch: true,
  //   //     }
  //   // })

  //   // let result = mapValues(groupBy(orders, order => order.shift.date), order => groupBy(order, o => o.shift.branch.code))
  //   const groupByDate = groupBy(orders, (order) => order.shift.date);

  //   const result = map(groupByDate, (orderGroup, date) => ({
  //     date: date,
  //     branchs: map(
  //       groupBy(orderGroup, (order) => order.shift.branch.code),
  //       (orders, branch) => ({
  //         branchCode: branch,
  //         orders,
  //       }),
  //     ),
  //   }));
  //   return result;
  //   const content = [];
  //   for (const data of result) {
  //     for (const branch of data.branchs) {
  //       const d = {
  //         date: data.date,
  //         branchCode: branch.branchCode,
  //       };
  //       content.push(d);
  //     }
  //   }

  //   return content;
  //   // const content = await this.orderService.report(startDate, endDate);

  //   // let data = content.map((e, i, _) => {
  //   //     const date = DateTime.fromJSDate(e.orderDate).setLocale('th-TH')

  //   //     return ({
  //   //         no: i + 1,
  //   //         order_no: e.orderNo,
  //   //         order_date: date.toLocaleString({ year: 'numeric', month: '2-digit', day: '2-digit' }),
  //   //         order_time: date.toLocaleString(DateTime.TIME_SIMPLE),
  //   //         order_status: e.convertStatusToThai(),
  //   //         discount: e.discount,
  //   //         grand_total: e.grandTotal,
  //   //         branch: e?.shift?.branch?.name ?? '-',
  //   //         cashier: e?.shift?.user?.fullName ?? '-',
  //   //     });
  //   // });

  //   // data.forEach((val, i, _) => {
  //   //     // console.log(val);
  //   //     worksheet.addRow(val)
  //   // })

  //   // worksheet.eachRow({ includeEmpty: true }, (row, rowNumber) => {
  //   //     if (rowNumber == 1) {
  //   //         row.eachCell((cell, cellNumber) => {
  //   //             cell.font = {
  //   //                 size: 14,
  //   //                 bold: true
  //   //             }
  //   //             cell.border = {
  //   //                 top: { style: 'thin' },
  //   //                 left: { style: 'thin' },
  //   //                 bottom: { style: 'thin' },
  //   //                 right: { style: 'thin' }
  //   //             }
  //   //         })
  //   //     }
  //   // })

  //   // const buffer = await workbook.xlsx.writeBuffer()
  //   return workbook.xlsx.writeBuffer();
  // }

  // async reportOrderBranch(startDate: Date, endDate: Date) {
  //   const orders = await this.orderRepository.find({
  //     where: {
  //       shift: {
  //         date: Between(startDate, endDate),
  //       },
  //       orderStatus: OrderStatus.COMPLETE,
  //     },
  //     relations: {
  //       shift: {
  //         branch: true,
  //       },
  //     },
  //   });

  //   const validOrders = orders.filter(
  //     (order) => order.shift && order.shift.branch,
  //   );

  //   // console.log(validOrders);

  //   const mapOrders = chain(validOrders)
  //     .groupBy((order) => order.shift.branch.code)
  //     .map((v, k) => ({
  //       branch: v[0].shift.branch,
  //       orders: v,
  //     }))
  //     .value();

  //   const currentDate = new Date(startDate);
  //   currentDate.setDate(currentDate.getDate() + 1);

  //   const dateList = [];
  //   while (currentDate <= endDate) {
  //     dateList.push(currentDate.toISOString().split('T')[0]);
  //     currentDate.setDate(currentDate.getDate() + 1);
  //   }

  //   // console.log(mapOrders);

  //   const result = [];
  //   for (const mapOrder of mapOrders) {
  //     if (!mapOrder.branch) {
  //       continue;
  //     }

  //     let groupByDate = chain(mapOrder.orders)
  //       .groupBy((order) => order.shift.date)
  //       .map((v, k) => ({
  //         date: k,
  //         billCount: v.length,
  //         total: v.reduce((acc, v) => acc + v.grandTotal, 0),
  //       }))
  //       .value();

  //     const dataDict: { [key: string]: any } = {};
  //     groupByDate.forEach((item) => {
  //       dataDict[item.date] = item;
  //     });

  //     groupByDate = dateList.map((date) => {
  //       return dataDict[date] || { date: date, billCount: 0, total: 0 };
  //     });

  //     result.push({
  //       branchCode: mapOrder.branch.code,
  //       branchName: mapOrder.branch.name,
  //       date: groupByDate,
  //     });
  //   }

  //   const workbook = new Workbook();

  //   const worksheet = workbook.addWorksheet('order');

  //   worksheet.properties.defaultColWidth = 15;

  //   const dateHearders = [];
  //   const totalHearders = [];
  //   for (const date of dateList) {
  //     dateHearders.push(date);
  //     dateHearders.push(date);

  //     totalHearders.push('ยอดบิล');
  //     totalHearders.push('ยอดขาย');
  //   }

  //   worksheet.insertRow(1, ['', '', ...dateHearders]);
  //   worksheet.insertRow(2, ['รหัสสาขา', 'ชื่อสาขา', ...totalHearders]);
  //   worksheet.getRow(1).font = {
  //     name: 'Cordia New',
  //     size: 22,
  //   };
  //   worksheet.getRow(2).font = {
  //     name: 'Cordia New',
  //     size: 22,
  //   };

  //   let startRow = 3;
  //   for (const value of result) {
  //     const totalWithDate = [];
  //     for (const date of value.date) {
  //       totalWithDate.push(date.billCount);
  //       totalWithDate.push(date.total);
  //     }

  //     worksheet.insertRow(startRow, [
  //       value.branchCode,
  //       value.branchName,
  //       ...totalWithDate,
  //     ]);
  //     worksheet.getRow(startRow).font = {
  //       name: 'Cordia New',
  //       size: 20,
  //     };
  //     startRow += 1;
  //   }
  //   return workbook.xlsx.writeBuffer();
  // }

  // async reportOrderBranchCategory(
  //   startDate: Date,
  //   endDate: Date,
  //   categoryId: number,
  // ) {
  //   const orders = await this.orderRepository.find({
  //     where: {
  //       shift: {
  //         date: Between(startDate, endDate),
  //       },
  //       orderStatus: OrderStatus.COMPLETE,
  //     },
  //     relations: {
  //       shift: {
  //         branch: true,
  //       },
  //       orderItems: {
  //         product: {
  //           category: true,
  //         },
  //       },
  //     },
  //   });
  //   // console.log(categoryId);

  //   const filteredOrders = orders.filter(
  //     (order) =>
  //       order.orderItems.some(
  //         (item) => item.product.category.id === categoryId,
  //       ) &&
  //       order.shift &&
  //       order.shift.branch,
  //   );

  //   const mapOrders = chain(filteredOrders)
  //     .groupBy((order) => order.shift.branch.code)
  //     .map((v, k) => ({
  //       branch: v[0].shift.branch,
  //       orders: v,
  //     }))
  //     .value();

  //   const currentDate = new Date(startDate);
  //   currentDate.setDate(currentDate.getDate() + 1);

  //   const dateList = [];
  //   while (currentDate <= endDate) {
  //     dateList.push(currentDate.toISOString().split('T')[0]);
  //     currentDate.setDate(currentDate.getDate() + 1);
  //   }

  //   const result = [];
  //   for (const mapOrder of mapOrders) {
  //     if (!mapOrder.branch) {
  //       continue;
  //     }

  //     let groupByDate = chain(mapOrder.orders)
  //       .groupBy((order) => order.shift.date)
  //       .map((v, k) => ({
  //         date: k,
  //         billCount: v.length,
  //         total: v.reduce((acc, v) => acc + v.grandTotal, 0),
  //       }))
  //       .value();

  //     groupByDate = chain(mapOrder.orders)
  //       .groupBy((order) => order.shift.date)
  //       .map((v, k) => ({
  //         date: k,
  //         billCount: v.reduce(
  //           (acc, order) =>
  //             acc +
  //             order.orderItems.filter(
  //               (item) => item.product.category.id === categoryId,
  //             ).length,
  //           0,
  //         ),
  //         categorycode: v[0].orderItems.filter(
  //           (item) => item.product.category.id === categoryId,
  //         )[0].product.category.code,
  //         categoryname: v[0].orderItems.filter(
  //           (item) => item.product.category.id === categoryId,
  //         )[0].product.category.name,
  //         productname: v[0].orderItems.filter(
  //           (item) => item.product.category.id === categoryId,
  //         )[0].product.name,
  //         productcode: v[0].orderItems.filter(
  //           (item) => item.product.category.id === categoryId,
  //         )[0].product.code,
  //         total: v.reduce(
  //           (acc, order) =>
  //             acc +
  //             order.orderItems
  //               .filter((item) => item.product.category.id === categoryId)
  //               .reduce(
  //                 (itemAcc, item) => itemAcc + item.price * item.quantity,
  //                 0,
  //               ),
  //           0,
  //         ),
  //       }))
  //       .value();

  //     result.push({
  //       branchCode: mapOrder.branch.code,
  //       branchName: mapOrder.branch.name,
  //       date: groupByDate,
  //     });
  //   }

  //   //format data
  //   type OrderEntry = {
  //     branchCode: string;
  //     branchName: string;
  //     date: string;
  //     billCount: number;
  //     categorycode: string;
  //     categoryname: string;
  //     productname: string;
  //     productcode: string;
  //     total: number;
  //   };
  //   function flattenAndSortByDate(data: any[]): OrderEntry[] {
  //     const flattenedData: OrderEntry[] = [];

  //     data.forEach((branch) => {
  //       branch.date.forEach((dateEntry: any) => {
  //         flattenedData.push({
  //           branchCode: branch.branchCode,
  //           branchName: branch.branchName,
  //           date: dateEntry.date,
  //           billCount: dateEntry.billCount,
  //           categorycode: dateEntry.categorycode,
  //           categoryname: dateEntry.categoryname,
  //           productname: dateEntry.productname,
  //           productcode: dateEntry.productcode,
  //           total: dateEntry.total,
  //         });
  //       });
  //     });

  //     // Sort the flattened data by date
  //     flattenedData.sort(
  //       (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime(),
  //     );

  //     return flattenedData;
  //   }

  //   const sortedFlattenedData = flattenAndSortByDate(result);

  //   sortedFlattenedData.forEach((value) => {
  //     // console.log(value);
  //   });

  //   const workbook = new Workbook();

  //   const worksheet = workbook.addWorksheet('order');

  //   worksheet.properties.defaultColWidth = 15;

  //   const metaHearders = [];
  //   metaHearders.push('วันที่');
  //   metaHearders.push('สาขา');
  //   metaHearders.push('ชื่อสาขา');
  //   metaHearders.push('กลุ่มสินค้า');
  //   metaHearders.push('ชื่อกลุ่มสินค้า');
  //   metaHearders.push('รหัสสินค้า');
  //   metaHearders.push('ชื่อสินค้า');
  //   metaHearders.push('จำนวน');
  //   metaHearders.push('มูลค่า');

  //   worksheet.insertRow(1, [...metaHearders]);
  //   worksheet.getRow(1).font = {
  //     name: 'Cordia New',
  //     size: 22,
  //   };

  //   let startRow = 2;
  //   for (const value of sortedFlattenedData) {
  //     worksheet.insertRow(startRow, [
  //       value.date,
  //       value.branchCode,
  //       value.branchName,
  //       value.categorycode,
  //       value.categoryname,
  //       value.productcode,
  //       value.productname,
  //       value.billCount,
  //       value.total,
  //     ]);
  //     worksheet.getRow(startRow).font = {
  //       name: 'Cordia New',
  //       size: 20,
  //     };
  //     startRow += 1;
  //   }
  //   return workbook.xlsx.writeBuffer();
  // }

  // async reportOrderBranchProduct(
  //   startDate: Date,
  //   endDate: Date,
  //   productId: number,
  // ) {
  //   const orders = await this.orderRepository.find({
  //     where: {
  //       shift: {
  //         date: Between(startDate, endDate),
  //       },
  //       orderStatus: OrderStatus.COMPLETE,
  //     },
  //     relations: {
  //       shift: {
  //         branch: true,
  //       },
  //       orderItems: {
  //         product: {
  //           category: true,
  //         },
  //       },
  //     },
  //   });

  //   // console.log(orders);

  //   const filteredOrders = orders.filter(
  //     (order) =>
  //       order.orderItems.some((item) => item.product.id === productId) &&
  //       order.shift &&
  //       order.shift.branch,
  //   );

  //   // console.log(filteredOrders);

  //   const mapOrders = chain(filteredOrders)
  //     .groupBy((order) => order.shift.branch.code)
  //     .map((v, k) => ({
  //       branch: v[0].shift.branch,
  //       orders: v,
  //     }))
  //     .value();

  //   const currentDate = new Date(startDate);
  //   currentDate.setDate(currentDate.getDate() + 1);

  //   const dateList = [];
  //   while (currentDate <= endDate) {
  //     dateList.push(currentDate.toISOString().split('T')[0]);
  //     currentDate.setDate(currentDate.getDate() + 1);
  //   }

  //   const result = [];
  //   for (const mapOrder of mapOrders) {
  //     if (!mapOrder.branch) {
  //       continue;
  //     }

  //     let groupByDate = chain(mapOrder.orders)
  //       .groupBy((order) => order.shift.date)
  //       .map((v, k) => ({
  //         date: k,
  //         billCount: v.length,
  //         total: v.reduce((acc, v) => acc + v.grandTotal, 0),
  //       }))
  //       .value();

  //     groupByDate = chain(mapOrder.orders)
  //       .groupBy((order) => order.shift.date)
  //       .map((v, k) => ({
  //         date: k,
  //         billCount: v.reduce(
  //           (acc, order) =>
  //             acc +
  //             order.orderItems.filter((item) => item.product.id === productId)
  //               .length,
  //           0,
  //         ),
  //         productPrice: v[0].orderItems.filter(
  //           (item) => item.product.id === productId,
  //         )[0].price,
  //         productname: v[0].orderItems.filter(
  //           (item) => item.product.id === productId,
  //         )[0].product.name,
  //         productcode: v[0].orderItems.filter(
  //           (item) => item.product.id === productId,
  //         )[0].product.code,
  //         total: v.reduce(
  //           (acc, order) =>
  //             acc +
  //             order.orderItems
  //               .filter((item) => item.product.id === productId)
  //               .reduce(
  //                 (itemAcc, item) => itemAcc + item.price * item.quantity,
  //                 0,
  //               ),
  //           0,
  //         ),
  //       }))
  //       .value();

  //     result.push({
  //       branchCode: mapOrder.branch.code,
  //       branchName: mapOrder.branch.name,
  //       date: groupByDate,
  //     });
  //   }

  //   //format data
  //   type OrderEntry = {
  //     branchCode: string;
  //     branchName: string;
  //     date: string;
  //     billCount: number;
  //     productPrice: number;
  //     productname: string;
  //     productcode: string;
  //     total: number;
  //     meanprice: number;
  //   };
  //   function flattenAndSortByDate(data: any[]): OrderEntry[] {
  //     const flattenedData: OrderEntry[] = [];

  //     data.forEach((branch) => {
  //       branch.date.forEach((dateEntry: any) => {
  //         flattenedData.push({
  //           branchCode: branch.branchCode,
  //           branchName: branch.branchName,
  //           date: dateEntry.date,
  //           billCount: dateEntry.billCount,
  //           productPrice: dateEntry.productPrice,
  //           productname: dateEntry.productname,
  //           productcode: dateEntry.productcode,
  //           total: dateEntry.total,
  //           meanprice: dateEntry.total / dateEntry.billCount,
  //         });
  //       });
  //     });

  //     // Sort the flattened data by date
  //     flattenedData.sort(
  //       (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime(),
  //     );

  //     return flattenedData;
  //   }

  //   const sortedFlattenedData = flattenAndSortByDate(result);

  //   sortedFlattenedData.forEach((value) => {
  //     // console.log(value);
  //   });

  //   const workbook = new Workbook();

  //   const worksheet = workbook.addWorksheet('order');

  //   worksheet.properties.defaultColWidth = 15;

  //   const metaHearders = [];
  //   metaHearders.push('วันที่');
  //   metaHearders.push('สาขา');
  //   metaHearders.push('ชื่อสาขา');
  //   metaHearders.push('รหัสสินค้า');
  //   metaHearders.push('ชื่อสินค้า');
  //   metaHearders.push('ราคาสินค้า');
  //   metaHearders.push('จำนวนชิ้น');
  //   metaHearders.push('ยอดขาย');
  //   metaHearders.push('ราคาเฉลี่ยที่ขายต่อจำนวนสินค้า');

  //   worksheet.insertRow(1, [...metaHearders]);
  //   worksheet.getRow(1).font = {
  //     name: 'Cordia New',
  //     size: 22,
  //   };

  //   let startRow = 2;
  //   let sumproduct = 0;
  //   let sumquantity = 0;
  //   let sumtotal = 0;
  //   let summean = 0;
  //   for (const value of sortedFlattenedData) {
  //     worksheet.insertRow(startRow, [
  //       value.date,
  //       value.branchCode,
  //       value.branchName,
  //       value.productcode,
  //       value.productname,
  //       value.productPrice,
  //       value.billCount,
  //       value.total,
  //       value.meanprice,
  //     ]);
  //     worksheet.getRow(startRow).font = {
  //       name: 'Cordia New',
  //       size: 20,
  //     };
  //     startRow += 1;
  //     sumproduct += value.productPrice;
  //     sumquantity += value.billCount;
  //     sumtotal += value.total;
  //     summean += value.meanprice;
  //   }
  //   worksheet.insertRow(startRow, [
  //     'Total',
  //     '',
  //     '',
  //     '',
  //     '',
  //     sumproduct,
  //     sumquantity,
  //     sumtotal,
  //     summean,
  //   ]);
  //   return workbook.xlsx.writeBuffer();
  // }

  // async reportOrderType(startDate: Date, endDate: Date) {
  //   const orders = await this.orderRepository.find({
  //     where: {
  //       shift: {
  //         date: Between(startDate, endDate),
  //       },
  //       orderPayments: {
  //         status: OrderPaymentStatus.SUCCESS,
  //       },
  //       orderStatus: OrderStatus.COMPLETE,
  //     },
  //     relations: {
  //       shift: {
  //         branch: true,
  //       },
  //       orderPayments: {
  //         paymentMethod: true,
  //       },
  //     },
  //   });

  //   const filteredOrders = orders.filter(
  //     (order) => order.shift && order.shift.branch,
  //   );

  //   const mapOrders = chain(filteredOrders)
  //     .groupBy((order) => order.shift.branch.code)
  //     .map((v, k) => ({
  //       branch: v[0].shift.branch,
  //       orders: v,
  //     }))
  //     .value();

  //   const result = [];
  //   for (const mapOrder of mapOrders) {
  //     if (!mapOrder.branch) {
  //       continue;
  //     }

  //     const groupByDate = chain(mapOrder.orders)
  //       .groupBy((order) => order.shift.date)
  //       .map((v, k) => ({
  //         date: k,
  //         entries: chain(v)
  //           .groupBy((order) => order.orderPayments[0].paymentMethod.name)
  //           .map((entries, type) => ({
  //             type: type,
  //             totalAmount: sumBy(entries, 'orderPayments[0].amount'),
  //             total: sumBy(entries, 'grandTotal'),
  //           }))
  //           .value(),
  //       }))
  //       .value();

  //     result.push({
  //       branchCode: mapOrder.branch.code,
  //       branchName: mapOrder.branch.name,
  //       date: groupByDate,
  //     });
  //   }

  //   // console.log(result);

  //   const groupedResult = result.reduce((acc, branch) => {
  //     branch.date.forEach((dateObj) => {
  //       const date = dateObj.date;
  //       if (!acc[date]) {
  //         acc[date] = {};
  //       }

  //       const groupedEntries = dateObj.entries.reduce((entryAcc, entry) => {
  //         if (!entryAcc[entry.type]) {
  //           entryAcc[entry.type] = [];
  //         }
  //         entryAcc[entry.type].push({
  //           branchCode: branch.branchCode,
  //           branchName: branch.branchName,
  //           ...entry,
  //         });
  //         return entryAcc;
  //       }, {});

  //       Object.entries(groupedEntries).forEach(([type, entries]) => {
  //         if (!acc[date][type]) {
  //           acc[date][type] = [];
  //         }
  //         acc[date][type] = [...acc[date][type], ...(entries as any)];
  //       });
  //     });
  //     return acc;
  //   }, {});

  //   //format data
  //   type OrderEntry = {
  //     branchCode: string;
  //     branchName: string;
  //     date: string;
  //     cash: number;
  //     member: number;
  //     qr: number;
  //     manee: number;
  //   };

  //   function flattenAndSortByDate(data: any[]): OrderEntry[] {
  //     const flattenedData: OrderEntry[] = [];

  //     data.forEach((branch) => {
  //       branch.date.forEach((dateEntry: any) => {
  //         // Initialize variables for each payment type
  //         let cash = 0,
  //           member = 0,
  //           qr = 0,
  //           manee = 0;

  //         dateEntry.entries.forEach((entry: any) => {
  //           switch (entry.type) {
  //             case 'เงินสด':
  //               cash = entry.totalAmount;
  //               break;
  //             case 'สมาชิก':
  //               member = entry.totalAmount;
  //               break;
  //             case 'พร้อมเพย์':
  //               qr = entry.totalAmount;
  //               break;
  //             case 'แม่มณี':
  //               manee = entry.totalAmount;
  //               break;
  //             default:
  //               console.warn(`Unknown type: ${entry.type}`);
  //           }
  //         });

  //         flattenedData.push({
  //           branchCode: branch.branchCode,
  //           branchName: branch.branchName,
  //           date: dateEntry.date,
  //           cash: cash,
  //           member: member,
  //           qr: qr,
  //           manee: manee,
  //         });
  //       });
  //     });

  //     return flattenedData.sort(
  //       (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime(),
  //     );
  //   }

  //   const sortedFlattenedData = flattenAndSortByDate(result);

  //   sortedFlattenedData.forEach((value) => {
  //     // console.log(value);
  //   });

  //   const workbook = new Workbook();

  //   const worksheet = workbook.addWorksheet('order');

  //   worksheet.properties.defaultColWidth = 15;

  //   const metaHearders = [];
  //   metaHearders.push('วันที่');
  //   metaHearders.push('รหัสสาขา');
  //   metaHearders.push('ชื่อสาขา');
  //   metaHearders.push('เงินสด\n CASH');
  //   metaHearders.push('พร้อมเพย์\n thaiqr');
  //   metaHearders.push('แม่มณี\n cash');
  //   metaHearders.push('สมาชิก\n member');
  //   metaHearders.push('รวมยอดเงิน');

  //   worksheet.insertRow(1, [...metaHearders]);
  //   worksheet.getRow(1).font = {
  //     name: 'Cordia New',
  //     size: 22,
  //   };

  //   let startRow = 2;

  //   let sumtotal = 0;
  //   let sumcash = 0;
  //   let sumqr = 0;
  //   let summanee = 0;
  //   let summember = 0;
  //   for (const value of sortedFlattenedData) {
  //     worksheet.insertRow(startRow, [
  //       value.date,
  //       value.branchCode,
  //       value.branchName,
  //       value.cash,
  //       value.qr,
  //       value.manee,
  //       value.member,
  //       value.cash + value.qr + value.manee + value.member,
  //     ]);
  //     worksheet.getRow(startRow).font = {
  //       name: 'Cordia New',
  //       size: 20,
  //     };
  //     startRow += 1;
  //     sumcash += value.cash;
  //     sumqr += value.qr;
  //     summanee += value.manee;
  //     summember += value.member;
  //     sumtotal += value.cash + value.qr + value.manee + value.member;
  //   }
  //   worksheet.insertRow(startRow, [
  //     '',
  //     '',
  //     'รวมทั้งหมด',
  //     sumcash,
  //     sumqr,
  //     summanee,
  //     summember,
  //     sumtotal,
  //   ]);
  //   worksheet.getRow(startRow).font = {
  //     name: 'Cordia New',
  //     size: 22,
  //   };
  //   return workbook.xlsx.writeBuffer();
  // }

  async reportProductSeller() {
    // const products = await this.productRepository.find({
    //   where: {
    //     orderItems: {
    //       order: {
    //         orderStatus: OrderStatus.COMPLETE,
    //       },
    //     },
    //   },
    //   relations: {
    //     orderItems: {
    //       order: true,
    //     },
    //     branches: true,
    //   },
    // });

    // // products.forEach(product => {
    // //     console.log(product.orderItems)
    // // })

    // const mapProducts = chain(products)
    //   .groupBy((product) => product.branches[0]?.code) // Group products by branch code
    //   .map((products, branchCode) => ({
    //     branch: products[0].branches, // Use the branch from the first product (all products in the group share the same branch)
    //     products: products.map((product) => {
    //       const totalQuantity = product.orderItems.reduce(
    //         (sum, item) => sum + item.quantity,
    //         0,
    //       );
    //       const totalValue = product.orderItems.reduce(
    //         (sum, item) => sum + item.quantity * item.price,
    //         0,
    //       );
    //       return {
    //         product: product,
    //         orderItems: product.orderItems,
    //         totalQuantity: totalQuantity,
    //         totalValue: totalValue,
    //       };
    //     }),
    //   }))
    //   .value();

    // mapProducts.forEach((value) => {
    //   // console.log(value);
    // });

    // const workbook = new Workbook();

    // const worksheet = workbook.addWorksheet('product');

    // worksheet.properties.defaultColWidth = 15;

    // const metaHearders = [];
    // metaHearders.push('รหัสสาขา');
    // metaHearders.push('ลำดับ');
    // metaHearders.push('รหัส SKU');
    // metaHearders.push('ชื่อ SKU');
    // metaHearders.push('จำนวน');
    // metaHearders.push('รวมราคา');

    // worksheet.insertRow(1, [...metaHearders]);
    // worksheet.getRow(1).font = {
    //   name: 'Cordia New',
    //   size: 22,
    // };

    // let startRow = 2;
    // mapProducts.forEach((value) => {
    //   value.products.forEach((product, index) => {
    //     const branchCode = value.branch[0]?.code || 'No Branch';
    //     worksheet.insertRow(startRow, [
    //       branchCode, // Keep the branch code for every row
    //       index + 1,
    //       product.product.code,
    //       product.product.name,
    //       product.totalQuantity,
    //       product.totalValue,
    //     ]);
    //     worksheet.getRow(startRow).font = {
    //       name: 'Cordia New',
    //       size: 20,
    //     };
    //     startRow += 1;
    //   });

    //   // Merge the branch code cells
    //   const startRowIndexOfBranch = startRow - value.products.length;
    //   const lastRowIndexOfBranch = startRow - 1;
    //   const mergedCellRange = `A${startRowIndexOfBranch}:A${lastRowIndexOfBranch}`;
    //   worksheet.mergeCells(mergedCellRange);
    //   worksheet.getCell(mergedCellRange).alignment = {
    //     vertical: 'middle',
    //     horizontal: 'center',
    //   };
    // });

    // return workbook.xlsx.writeBuffer();
  }

  // async reportItemSaleTransaction(
  //   startDate: Date,
  //   endDate: Date,
  //   branchCode: string,
  // ) {
  //   const orders = await this.orderRepository.find({
  //     where: {
  //       shift: {
  //         date: Between(startDate, endDate),
  //         branch: {
  //           code: branchCode,
  //         },
  //       },
  //       orderPayments: {
  //         status: OrderPaymentStatus.SUCCESS,
  //       },
  //       orderStatus: OrderStatus.COMPLETE,
  //     },
  //     relations: {
  //       shift: {
  //         branch: true,
  //       },
  //       orderItems: {
  //         product: {
  //           category: true,
  //           unit: true,
  //         },
  //       },
  //       orderPayments: {
  //         paymentMethod: true,
  //       },
  //     },
  //   });

  //   const filteredOrders = orders
  //     .filter(
  //       (order) =>
  //         order &&
  //         order.shift &&
  //         order.shift.branch &&
  //         order.orderItems.some((item) => item.product),
  //     )
  //     .map((order) => {
  //       // Filter out orderItems without a product
  //       order.orderItems = order.orderItems.filter((item) => item.product);
  //       return order;
  //     });

  //   // filteredOrders.forEach(order => {
  //   //     console.log(order);

  //   // })

  //   const mapOrders = chain(filteredOrders)
  //     .groupBy((order) => order.shift.branch.code)
  //     .map((v, k) => ({
  //       branch: v[0].shift.branch,
  //       orders: v,
  //     }))
  //     .value();

  //   // mapOrders.forEach(value => {
  //   //     console.log(value);
  //   // })

  //   const result = [];
  //   for (const mapOrder of mapOrders) {
  //     if (!mapOrder.branch) {
  //       continue;
  //     }

  //     const groupByDate = chain(mapOrder.orders)
  //       .groupBy((order) => order.shift.date)
  //       .map((v, k) => ({
  //         date: k,
  //         entries: v,
  //       }))
  //       .value();

  //     result.push({
  //       branchCode: mapOrder.branch.code,
  //       branchName: mapOrder.branch.name,
  //       date: groupByDate,
  //     });
  //   }

  //   // console.log(result);

  //   result.forEach((order) => {
  //     order.date.forEach((date) => {
  //       // console.log(date);
  //       date.entries.forEach((entries) => {
  //         // console.log(entries);
  //         entries.orderItems.forEach((item) => {
  //           // console.log(item);
  //         });
  //         entries.orderPayments.forEach((payment) => {
  //           // console.log(payment);
  //         });
  //       });
  //     });
  //     // console.log(order);
  //   });

  //   //format data
  //   type OrderEntry = {
  //     date: string;
  //     outlet: string;
  //     orderdate: string;
  //     Receipt: string;
  //     productcode: string;
  //     productname: string;
  //     unit: string;
  //     quantity: number;
  //     price: number;
  //     total: number;
  //     category: string;
  //     seller: string;
  //     shift: string;
  //     paymentType: string;
  //     wallettype: string;
  //     paidDate: string;
  //     orderstatus: string;
  //     empcode: string;
  //     empname: string;
  //     cardtype: string;
  //     remark: string;
  //   };

  //   function flattenAndSortByDate(data: any[]): OrderEntry[] {
  //     const flattenedData: OrderEntry[] = [];

  //     data.forEach((branch) => {
  //       branch.date.forEach((dateEntry: any) => {
  //         // Initialize variables for each payment type

  //         dateEntry.entries.forEach((entry: any) => {
  //           flattenedData.push({
  //             date: dateEntry.date,
  //             outlet: '',
  //             orderdate: entry.orderDate,
  //             Receipt: entry.orderNo,
  //             productcode: entry.orderItems[0].product.code,
  //             productname: entry.orderItems[0].product.name,
  //             unit: entry.orderItems[0].product.unit.name,
  //             quantity: entry.orderItems[0].quantity,
  //             price: entry.orderItems[0].price,
  //             total: entry.orderItems[0].total,
  //             category: entry.orderItems[0].product.category.name,
  //             seller: 'Admin admin',
  //             shift: '',
  //             paymentType: entry.orderPayments[0].paymentMethod.name,
  //             wallettype: '',
  //             paidDate: '',
  //             orderstatus: '',
  //             empcode: '',
  //             empname: '',
  //             cardtype: '',
  //             remark: '',
  //           });
  //         });
  //       });
  //     });

  //     return flattenedData.sort(
  //       (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime(),
  //     );
  //   }

  //   const sortedFlattenedData = flattenAndSortByDate(result);

  //   // sortedFlattenedData.forEach(value => {
  //   //     console.log(value);
  //   // });

  //   const workbook = new Workbook();

  //   const worksheet = workbook.addWorksheet('order');

  //   worksheet.properties.defaultColWidth = 15;

  //   const metaHearders = [];
  //   metaHearders.push('Date');
  //   metaHearders.push('Outlet/Station');
  //   metaHearders.push('Order DateTime');
  //   metaHearders.push('Receipt/Bill No.');
  //   metaHearders.push('รหัสสินค้า');
  //   metaHearders.push('ชื่อสินค้า');
  //   metaHearders.push('หน่วย');
  //   metaHearders.push('QTY');
  //   metaHearders.push('Price');
  //   metaHearders.push('Sub Total');
  //   metaHearders.push('ประเภทสินค้า/Category');
  //   metaHearders.push('ชื่อผู้ขาย');
  //   metaHearders.push('กะการขาย');
  //   metaHearders.push('Payment Type');
  //   metaHearders.push('Wallet Type');
  //   metaHearders.push('Paid DateTime');
  //   metaHearders.push('Status');
  //   metaHearders.push('รหัสพนักงาน');
  //   metaHearders.push('ชื่อพนักงาน');
  //   metaHearders.push('Card Type');
  //   metaHearders.push('Remark');

  //   worksheet.insertRow(1, [
  //     'รายงานแสดงรายการการขาย (ITEM Sale Transaction Report)',
  //   ]);
  //   worksheet.getRow(1).font = {
  //     name: 'Cordia New',
  //     size: 24,
  //     bold: true,
  //   };

  //   worksheet.insertRow(3, [
  //     'ชื่อสาขา',
  //     result[0].branchName,
  //     'รหัสสาขา',
  //     result[0].branchCode,
  //   ]);
  //   worksheet.getRow(1).font = {
  //     name: 'Cordia New',
  //     size: 20,
  //     bold: true,
  //   };

  //   worksheet.insertRow(4, [
  //     'ประจำวันที่',
  //     startDate + ' ถึงวันที่ ' + endDate,
  //   ]);
  //   worksheet.getRow(1).font = {
  //     name: 'Cordia New',
  //     size: 20,
  //     bold: true,
  //   };

  //   worksheet.insertRow(6, [...metaHearders]);
  //   const header = worksheet.getRow(6);
  //   header.font = {
  //     name: 'Cordia New',
  //     size: 20,
  //     bold: true,
  //   };
  //   header.eachCell((cell, colNumber) => {
  //     cell.fill = {
  //       type: 'pattern',
  //       pattern: 'solid',
  //       fgColor: { argb: 'C0C0C0' }, // Gray fill color
  //     };
  //   });

  //   let startRow = 7;

  //   for (const value of sortedFlattenedData) {
  //     worksheet.insertRow(startRow, [
  //       value.date,
  //       value.outlet,
  //       value.orderdate,
  //       value.Receipt,
  //       value.productcode,
  //       value.productname,
  //       value.unit,
  //       value.quantity,
  //       value.price,
  //       value.total,
  //       value.category,
  //       value.seller,
  //       value.shift,
  //       value.paymentType,
  //       value.wallettype,
  //       value.paidDate,
  //       value.orderstatus,
  //       value.empcode,
  //       value.empname,
  //       value.cardtype,
  //       value.remark,
  //     ]);
  //     worksheet.getRow(startRow).font = {
  //       name: 'Cordia New',
  //       size: 20,
  //     };
  //     startRow += 1;
  //   }

  //   return workbook.xlsx.writeBuffer();
  // }

  async reportTopUpHistory(startDate: Date, endDate: Date) {
    const workbook = new Workbook();

    const worksheet = workbook.addWorksheet('Data', {});

    const HEADER_FONT_STYLE = { bold: true };
    const HEADER_FILL_STYLE: Fill = {
      type: 'pattern',
      pattern: 'solid',
      bgColor: { argb: 'FFFF00' },
      fgColor: { argb: 'FFFF00' },
    };

    const SUBTOTAL_FONT_STYLE = { bold: true };
    const SUBTOTAL_FILL_STYLE: ExcelJS.Fill = {
      type: 'pattern',
      pattern: 'solid',
      bgColor: { argb: 'FFFF00' },
      fgColor: { argb: 'FFFF00' },
    };

    worksheet.columns = [
      { header: 'Date', key: 'date', width: 15 },
      { header: 'Receipt No', key: 'date', width: 15 },
      { header: 'S/N Card (RFID)', key: 'date', width: 15 },
      { header: 'Employee No.', key: 'date', width: 15 },
      { header: 'Name', key: 'date', width: 30 },
      { header: 'Card Type', key: 'date', width: 15 },
      { header: 'Wallet Type', key: 'date', width: 15 },
      { header: 'Top-up by', key: 'date', width: 15 },
      { header: 'Date/Time', key: 'date', width: 15 },
      { header: 'Status', key: 'date', width: 15 },
      { header: 'Top-up Amount', key: 'date', width: 15 },
    ];
    const header = worksheet.getRow(1);
    header.eachCell((c, n) => {
      c.fill = HEADER_FILL_STYLE;
      c.font = HEADER_FONT_STYLE;
      c.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
    });

    const paymentRepository = this.dataSource.manager.getRepository(Payment);
    // const content = await paymentRepository.find({
    //     where: {
    //         paymentType:
    //     }
    // })
    const content = [];

    let lastDate = ''; // ตัวแปรช่วยเพื่อเก็บวันที่ล่าสุดที่ได้เพิ่มเข้าไป
    const data = [];
    // Add rows using the data provided
    data.forEach((item) => {
      const row = worksheet.addRow({
        date: item.date !== lastDate ? item.date : '',
        description: item.description,
        tabCount: item.tabCount,
        amount: item.amount,
        vat: item.vat,
        totalAmount: item.totalAmount,
      });

      // อัปเดต lastDate เป็นวันที่ปัจจุบันถ้าไม่ซ้ำ
      if (item.date !== lastDate) {
        lastDate = item.date;
      }

      // ใส่เส้นให้กับทุกเซลล์ในแถว
      row.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin', color: { argb: '000000' } },
          left: { style: 'thin', color: { argb: '000000' } },
          bottom: { style: 'thin', color: { argb: '000000' } },
          right: { style: 'thin', color: { argb: '000000' } },
        };
      });
    });
    const startDateFormatted: string = DateTime.fromJSDate(startDate, {
      zone: 'utc',
    }).toFormat('dd/MM/yyyy');
    const endDateFormatted: string = DateTime.fromJSDate(endDate, {
      zone: 'utc',
    }).toFormat('dd/MM/yyyy');
    const nowDate = DateTime.now()
      .setLocale('th-TH')
      .toLocaleString({ ...DateTime.DATE_SHORT, month: '2-digit' });
    const combinedDate = `${startDateFormatted} ถึงวันที่ ${endDateFormatted}`;
    worksheet.insertRow(1, ['บริษัทโซเด็กซ์โซ่ อมตะ เซอร์วิสเซส จำกัด']);
    worksheet.mergeCells('A1:K1');
    worksheet.getCell('A1').font = { size: 14, bold: true };
    worksheet.getCell('A1').alignment = {
      horizontal: 'center',
      vertical: 'middle',
    };

    worksheet.insertRow(2, ['รายงานประวัติการเติมเงินของแต่ละบัตร']);
    worksheet.mergeCells('A2:K2');
    worksheet.getCell('A2').font = { size: 14, bold: true };
    worksheet.getCell('A2').alignment = {
      horizontal: 'center',
      vertical: 'middle',
    };
    worksheet.getCell('A2').fill = {
      type: 'pattern',
      pattern: 'solid',
      bgColor: { argb: 'D3D3D3' },
      fgColor: { argb: 'D3D3D3' },
    };

    worksheet.insertRow(3, ['']);
    worksheet.mergeCells('A3:F3');

    worksheet.insertRow(4, ['ชื่อสาขา', 'Essilor']);
    worksheet.getCell('A4').font = { size: 11, bold: true };
    worksheet.getCell('A4').alignment = {
      horizontal: 'left',
      vertical: 'middle',
    };

    worksheet.insertRow(5, ['รหัสสาขา', '1083TAT101152']);
    worksheet.getCell('A5').font = { size: 11, bold: true };
    worksheet.getCell('A5').alignment = {
      horizontal: 'left',
      vertical: 'middle',
    };

    worksheet.insertRow(6, [
      'ประจำวันที่',
      combinedDate,
      '',
      '',
      'พิมพ์',
      nowDate,
    ]);
    worksheet.getCell('A6').font = { size: 11, bold: true };
    worksheet.getCell('A6').alignment = {
      horizontal: 'left',
      vertical: 'middle',
    };
    worksheet.getCell('E6').font = { size: 11, bold: true };
    worksheet.getCell('E6').alignment = {
      horizontal: 'left',
      vertical: 'middle',
    };

    worksheet.insertRow(7, ['']);
    worksheet.insertRow(8, ['']);

    // Add subtotals row manually if needed
    const subTotalRow = worksheet.addRow([
      '',
      'Sub Total',
      data.reduce((acc, cur) => acc + cur.tabCount, 0),
      data.reduce((acc, cur) => acc + cur.amount, 0),
      data.reduce((acc, cur) => acc + cur.vat, 0),
      data.reduce((acc, cur) => acc + cur.totalAmount, 0),
    ]);

    subTotalRow.eachCell((cell, index) => {
      cell.fill = SUBTOTAL_FILL_STYLE;
      cell.font = SUBTOTAL_FONT_STYLE;
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.border = {
        top: { style: 'thin', color: { argb: '000000' } },
        left: { style: 'thin', color: { argb: '000000' } },
        bottom: { style: 'thin', color: { argb: '000000' } },
        right: { style: 'thin', color: { argb: '000000' } },
      };
    });
    // Write to buffer
    return workbook.xlsx.writeBuffer();
  }

  async reportTopup(startDate: Date, endDate: Date) {
    const members = await Member.find({
      where: {
        payments: {
          createdAt: Between(startDate, endDate),
          paymentType: PaymentType.TOPUP,
        },
      },
      relations: {
        // card: true,
        payments: true,
      },
      order: {
        payments: {
          createdAt: 'ASC',
        },
      },
    });

    const data = [];

    for (const member of members) {
      const m = {
        sn: member.sn,
        type: member.cardType,
        memberNo: member.code,
        firstname: member.firstname,
        lastname: member.lastname,
        topups: [],
      };

      const topups = [];
      for (const payment of member.payments) {
        const topup = {
          date: datetime2string(payment.createdAt).datetime,
          refNo: payment.referenceNo,
          total: payment.total,
          status: payment.status,
        };
        topups.push(topup);
      }

      m.topups = topups;
      data.push(m);
    }

    const workbook = new Workbook();

    // Create two worksheets
    const worksheet = workbook.addWorksheet('Report Topup');

    worksheet.addRow(['เลขบัตร', 'ประเภท', 'รหัสพนักงาน', 'ชื่อ', 'นามสกุล']);
    worksheet.addRow(['', 'วันที่/เวลา', 'Refno', 'จำนวน', 'สถานะ']);

    const headers = worksheet.getRows(1, 2);
    for (const header of headers) {
      const TITLE_FILL_STYLE: Fill = {
        type: 'pattern',
        pattern: 'solid',
        bgColor: { argb: 'D3D3D3' },
        fgColor: { argb: 'D3D3D3' },
      };
      header.fill = TITLE_FILL_STYLE;
    }

    for (const item of data) {
      worksheet.addRow([
        item.sn,
        item.type,
        item.memberNo,
        item.firstname,
        item.lastname,
      ]);

      for (const topup of item.topups) {
        worksheet.addRow([
          '',
          topup.date,
          topup.refNo,
          topup.total,
          topup.status,
        ]);
      }
    }

    return workbook.xlsx.writeBuffer();
  }

  addHeader(
    startDate: Date,
    endDate: Date,
    title: string,
    worksheet: ExcelJS.Worksheet,
  ) {
    const startDateFormatted: string = DateTime.fromJSDate(startDate, {
      zone: 'Asia/Bangkok',
    }).toFormat('dd/MM/yyyy');
    const endDateFormatted: string = DateTime.fromJSDate(endDate, {
      zone: 'Asia/Bangkok',
    }).toFormat('dd/MM/yyyy');
    const nowDate = DateTime.now()
      .setZone('Asia/Bangkok')
      .toFormat('dd/MM/yyyy');
    worksheet.insertRow(1, ['']);

    worksheet.insertRow(2, ['บริษัท โซเด็กซ์โซ่ อมตะ เซอร์วิสเซส จำกัด']);
    worksheet.mergeCells('A2:I2');
    worksheet.getCell('A2').font = { size: 14, bold: true };
    worksheet.getCell('A2').alignment = {
      horizontal: 'center',
      vertical: 'middle',
    };

    worksheet.insertRow(3, [title]);
    worksheet.mergeCells('A3:I3');
    worksheet.getCell('A3').font = { size: 14, bold: true };
    worksheet.getCell('A3').alignment = {
      horizontal: 'center',
      vertical: 'middle',
    };
    worksheet.getCell('A3').fill = {
      type: 'pattern',
      pattern: 'solid',
      bgColor: { argb: 'D3D3D3' },
      fgColor: { argb: 'D3D3D3' },
    };

    worksheet.insertRow(4, ['']);
    // worksheet.mergeCells('B4:F4')

    worksheet.insertRow(5, [
      'ชื่อสาขา',
      'Essilor',
      'ประเภทกระเป๋า',
      'All, EL2 OT Credit, EL3 Personal Wallet, EL4 VIP Credit',
    ]);
    worksheet.getCell('A5').font = { size: 11, bold: true };
    worksheet.getCell('A5').alignment = {
      horizontal: 'left',
      vertical: 'middle',
    };

    worksheet.insertRow(6, [
      'ประจำวันที่',
      startDateFormatted,
      'ถึงวันที่',
      endDateFormatted,
      '',
      '',
      '',
      'พิมพ์',
      nowDate,
    ]);
    worksheet.getCell('A6').font = { size: 11, bold: true };
    worksheet.getCell('A6').alignment = {
      horizontal: 'left',
      vertical: 'middle',
    };
    worksheet.getCell('C6').font = { size: 11, bold: true };
    worksheet.getCell('C6').alignment = {
      horizontal: 'left',
      vertical: 'middle',
    };
    worksheet.getCell('H7').font = { size: 11, bold: true };
    worksheet.getCell('H7').alignment = {
      horizontal: 'left',
      vertical: 'middle',
    };

    worksheet.insertRow(7, ['']);
  }

  // async searchDevices(startDate: Date, endDate: Date, devices: Order[] ) {
  //   const showdevice = await this.orderRepository.find({
  //         where: {
  //           orderDate: Between(startDate, endDate),
  //           orderStatus: OrderStatus.COMPLETE,
  //         },
  //         relations: ['device'],
  //       });
  //       if(!showdevice){
  //         throw new BadRequestException('Device not found');
  //       }
  //       console.log(showdevice)

  //   return showdevice;
  // }
  // async searchReserve(startDate: Date, endDate: Date, orderType: Order[] ) {
  //   const showReserve = await this.orderRepository.find({
  //         where: {
  //           orderType: OrderType.RESERVE,
  //           orderDate: Between(startDate, endDate),
  //           orderStatus: OrderStatus.COMPLETE,
  //         },
  //       });
  //       if(!showReserve){
  //         throw new BadRequestException('Device not found');
  //       }

  //   return showReserve;
  // }

  // async getBuffetByType(foodType: FoodType, startDate: Date, endDate?: Date): Promise<Buffet[]> {

  //   const start = new Date(startDate);
  //   start.setHours(0, 0, 0, 0);

  //   const end = endDate ? new Date(endDate) : new Date(startDate);
  //   end.setHours(23, 59, 59, 999);

  //   const buffetLogs = await this.buffetRepository.find({
  //     where: {
  //       foodType,
  //       timestamp: Between(start, end),
  //     },
  //     relations: ['member'],
  //   });

  //   if (!buffetLogs.length) {
  //     throw new NotFoundException(`No buffet logs found for food type: ${foodType} between ${start} and ${end}`);
  //   }

  //   return buffetLogs;
  // }

  async searchDevicesExcel(startDate: Date, endDate: Date, deviceId: number) {
    const showdevice = await this.orderRepository.find({
      where: {
        orderDate: Between(startDate, endDate),
        orderStatus: In([OrderStatus.COMPLETE, OrderStatus.VOID]),
        device: {
          id: deviceId,
        },
      },
      relations: { device: true },
    });

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Device History');

    worksheet.columns = [
      { header: 'ออเดอร์ ลำดับที่', key: 'id', width: 10 },
      {
        header: 'สร้างเมื่อ',
        key: 'createdAt',
        width: 20,
        style: { numFmt: 'yyyy-mm-dd hh:mm:ss' },
      },
      { header: 'เลขที่ ออเดอร์', key: 'orderNo', width: 15 },
      {
        header: 'วันที่ ออเดอร์',
        key: 'orderDate',
        width: 20,
        style: { numFmt: 'yyyy-mm-dd' },
      },
      { header: 'สถานะ ออเดอร์', key: 'orderStatus', width: 15 },
      { header: 'ประเภท ออเดอร์', key: 'orderType', width: 15 },
      { header: 'จำนวนที่จ่าย', key: 'paid', width: 10 },
      { header: 'รวมทั้งหมด', key: 'grandTotal', width: 15 },
      { header: 'หมายเหตุ', key: 'remark', width: 20 },
      { header: 'ชื่อเครื่อง', key: 'deviceName', width: 20 },
    ];

    for (const order of showdevice) {
      worksheet.addRow({
        id: order.id || '',
        createdAt: datetime2string(order?.createdAt).datetime,
        orderNo: order.orderNo || '',
        orderDate: datetime2string(order.orderDate).datetime,
        orderStatus: order.orderStatus || '',
        paid: order.paid || '',
        grandTotal: order.grandTotal || '',
        remark: order.remark || '',
        deviceName: order.device ? order.device.name : '',
      });
    }

    return workbook.xlsx.writeBuffer();
  }

  async searchReserveExcel(startDate: Date, endDate: Date) {
    startDate = DateTime.fromJSDate(startDate).startOf('day').toJSDate();
    endDate = DateTime.fromJSDate(endDate).endOf('day').toJSDate();

    const showReserve = await this.orderRepository.find({
      where: {
        orderDate: Between(startDate, endDate),
        orderStatus: OrderStatus.COMPLETE,
      },
      relations: {
        member: true,
        orderItems: {
          product: true,
        },
      },
    });

    if (!showReserve || showReserve.length === 0) {
      throw new BadRequestException(
        'No orders found for the specified criteria',
      );
    }

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Device History');

    worksheet.columns = [
      { header: 'Order No', key: 'orderNo', width: 15 },
      {
        header: 'Order Date',
        key: 'orderDate',
        width: 20,
        style: { numFmt: 'yyyy-mm-dd' },
      },
      { header: 'Code', key: 'employee_no', width: 20 },
      { header: 'Firstname', key: 'firstname', width: 20 },
      { header: 'Lastname', key: 'lastname', width: 20 },
      { header: 'Menu', key: 'productNames', width: 30 },
      { header: 'Status', key: 'orderStatus', width: 15 },
      { header: 'Total', key: 'grandTotal', width: 15 },
    ];

    const borderStyle: Partial<ExcelJS.Borders> = {
      top: { style: 'thin' as ExcelJS.BorderStyle },
      left: { style: 'thin' as ExcelJS.BorderStyle },
      bottom: { style: 'thin' as ExcelJS.BorderStyle },
      right: { style: 'thin' as ExcelJS.BorderStyle },
    };

    // Apply styles to the header row
    const headerRow = worksheet.getRow(1);
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFFF00' },
      }; // Yellow background
      cell.border = borderStyle;
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
      cell.font = { bold: true };
    });

    let totalSum = 0; // Variable to keep track of the total sum of grandTotal

    for (const order of showReserve) {
      const productNames = order.orderItems
        .map((item) => item.product.name)
        .join(', ');

      // If the order status is void, make the grandTotal negative
      const grandTotal =
        order.orderStatus === OrderStatus.VOID
          ? -Math.abs(order.grandTotal || 0)
          : order.grandTotal || 0;

      totalSum += grandTotal; // Add the current order's grandTotal to the total sum

      const row = worksheet.addRow({
        orderNo: order.orderNo || '',
        orderDate: datetime2string(order.orderDate).datetime,
        employee_no: order.member.code,
        firstname: order.member.firstname,
        lastname: order.member.lastname,
        productNames: productNames || '',
        orderStatus: order.orderStatus || '',
        grandTotal: grandTotal,
      });

      // Apply borders to each cell in the row
      row.eachCell((cell) => {
        cell.border = borderStyle;
      });
    }

    // Add the total row at the very end
    const lastRowIndex = worksheet.lastRow.number + 1;
    const totalRow = worksheet.addRow({
      orderNo: 'Total', // Label this row as 'Total'
      orderDate: '',
      employee_no: '',
      firstname: '',
      lastname: '',
      productNames: '', // Empty cells for the rest of the row
      orderStatus: '',
      grandTotal: totalSum, // The calculated total sum of grandTotal
    });

    worksheet.mergeCells(`A${lastRowIndex}:G${lastRowIndex}`);

    const totalCell = worksheet.getCell(`A${lastRowIndex}`);
    totalCell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'e6e6e6' },
    };
    totalCell.font = { bold: true, color: { argb: '000000' } };
    totalCell.alignment = { horizontal: 'right', vertical: 'middle' };
    totalCell.border = borderStyle;

    const totalAmountCell = worksheet.getCell(`H${lastRowIndex}`);
    totalAmountCell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'e6e6e6' },
    };
    totalAmountCell.font = { bold: true, color: { argb: '000000' } };
    totalAmountCell.alignment = { horizontal: 'center', vertical: 'middle' };
    totalAmountCell.border = borderStyle;

    // Apply borders to the total row
    totalRow.eachCell((cell) => {
      cell.border = borderStyle;
    });

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async reportSaleOrder(
    startDate: Date,
    endDate: Date,
    deviceIds?: number[],
    paymentMethods?: number[],
    includeBuffet: boolean = false, // Default to false
  ) {
    // Sale Order Data Query
    const queryBuilder = this.orderItemRepository
      .createQueryBuilder('oi')
      .select([
        'oi.id AS order_item_id',
        'o.order_date',
        'd.name',
        'o.order_no',
        'c.name AS category_name',
        'p.code AS product_code',
        'p.name AS product_name',
        'un.name AS unit_name',
        `CONCAT(usr.first_name, ' ', usr.last_name) AS sell_name`,
        'oi.quantity',
        'oi.price',
        'oi.total',
        'pm.name AS payment_method_name', // Use payment method name instead of ID
        'o.order_status AS status',
        'm.code AS code',
        'm.card_type AS type',
        `CONCAT(m.firstname, ' ', m.middlename, ' ', m.lastname) AS buyer_name`,
        'py.reference_no AS refno',
      ])
      .innerJoin('oi.product', 'p')
      .innerJoin('oi.order', 'o')
      .innerJoin('o.orderPayments', 'op')
      .innerJoin('op.paymentMethod', 'pm')
      .leftJoin('payment', 'py', 'py.order_payment_id = op.id')
      .leftJoin('o.member', 'm')
      .leftJoin('o.device', 'd')
      .leftJoin('p.category', 'c')
      .leftJoin('p.unit', 'un')
      .leftJoin('o.user', 'usr')
      .where('op.status = :status', { status: 'success' })
      .andWhere('o.order_status IN (:...statuses)', { statuses: ['complete'] })
      .andWhere('oi.created_at BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .andWhere('o.device_id IN (:...deviceIds)', { deviceIds })
      .andWhere('pm.id IN (:...paymentMethods)', { paymentMethods })
      .getRawMany();

    const results = await queryBuilder;
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('RepLogCardPayment_Sum', {
      views: [{ showGridLines: false }],
    });
    const startDateFormatted: string = DateTime.fromJSDate(startDate, {
      zone: 'Asia/Bangkok',
    }).toFormat('dd/MM/yyyy');
    const endDateFormatted: string = DateTime.fromJSDate(endDate, {
      zone: 'Asia/Bangkok',
    }).toFormat('dd/MM/yyyy');

    worksheet.insertRow(1, ['']);
    worksheet.insertRow(2, [
      'รายงานแสดงรายการการขาย (ITEM Sale Transaction Report)',
    ]);
    worksheet.mergeCells('A2:D2');
    worksheet.getCell('A2').font = { size: 14, bold: true };
    worksheet.getCell('A2').alignment = {
      horizontal: 'left',
      vertical: 'middle',
    };
    worksheet.getCell('A2').fill = {
      type: 'pattern',
      pattern: 'solid',
      bgColor: { argb: 'D3D3D3' },
      fgColor: { argb: 'D3D3D3' },
    };
    worksheet.insertRow(4, [
      `ประจำวันที่ ${startDateFormatted} ถึงวันที่ ${endDateFormatted}`,
    ]);
    worksheet.getRow(4).eachCell((cell) => {
      cell.font = { size: 11, bold: true };
      cell.alignment = { horizontal: 'left', vertical: 'middle' };
    });

    worksheet.insertRow(5, ['']);
    worksheet.properties.defaultColWidth = 15;

    // Insert the main header row with merged cells for "Name" and "Type"
    worksheet.mergeCells('A6:B6');
    worksheet.getCell('A6').value = 'Name';
    worksheet.getCell('A6').font = { name: 'Arial', size: 9, bold: true };
    worksheet.getCell('A6').alignment = {
      horizontal: 'center',
      vertical: 'middle',
    };
    worksheet.getCell('A6').fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFCCCCCC' },
    };
    worksheet.getCell('A6').border = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' },
    };

    worksheet.getCell('C6').value = 'Type';
    worksheet.getCell('C6').font = { name: 'Arial', size: 9, bold: true };
    worksheet.getCell('C6').alignment = {
      horizontal: 'center',
      vertical: 'middle',
    };
    worksheet.getCell('C6').fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFCCCCCC' },
    };
    worksheet.getCell('C6').border = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' },
    };

    // Adjust rows to reflect correct headers under "Name" and "Type"
    const metaHeaders = [
      'Date',
      'Outlet/Station',
      'Order DateTime',
      'Ref No.',
      'Receipt No.',
      'Product Code',
      'Name',
      'Unit',
      'Qty',
      'Price',
      'Sub Total',
      'Category',
      'Seller',
      'Payment Type',
      'Status',
      'Code',
      'Name',
      'Type',
    ];

    const headerRow = worksheet.insertRow(7, metaHeaders);
    headerRow.eachCell((c) => {
      c.font = { name: 'Arial', size: 9, bold: true };
      c.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      c.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFCCCCCC' },
      };
    });

    let totalSaleOrder = 0;
    for (const item of results) {
      const formattedDate = DateTime.fromJSDate(
        new Date(item.order_date),
      ).toFormat('dd/MM/yyyy');
      const orderDate = DateTime.fromJSDate(new Date(item.order_date)).toFormat(
        'dd/MM/yyyy HH:mm',
      );
      const row = worksheet.addRow([
        formattedDate,
        item.d_name || '-',
        orderDate,
        item.refno || '-',
        item.order_no ?? '-',
        item.product_code ?? '-',
        item.product_name ?? '-',
        item.unit_name ?? '-',
        item.oi_quantity ?? 0,
        item.oi_price ?? 0,
        item.oi_total ?? 0,
        item.category_name ?? '-',
        item.sell_name,
        item.payment_method_name || '-', // Display payment method name or '-' if undefined
        item.status ?? '-',
        item.code ?? '-',
        item.buyer_name ?? '-',
        item.type || '-', // Ensure 'Type' is never blank; use '-' if undefined
      ]);

      row.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      });

      totalSaleOrder += Number(item.oi_total);
    }

    let totalBuffetPrice = 0;

    if (includeBuffet) {
      const start = new Date(startDate);
      start.setHours(0, 0, 0, 0);

      const end = endDate ? new Date(endDate) : new Date(startDate);
      end.setHours(23, 59, 59, 999);

      const whereCondition = {
        timestamp: Between(start, end),
        // status: BuffetStatus.SUCCESS, // Add status condition to only include successful buffet logs
      };

      // Buffet Data Query
      // const buffetLogs = await this.buffetRepository.find({
      //   where: whereCondition,
      //   relations: ['member'],
      // });

      // if (buffetLogs.length > 0) {
      //   // Ensure buffetLogs are processed only if data exists
      //   buffetLogs.forEach((buffet) => {
      //     const timestampUTC7 = DateTime.fromJSDate(buffet.timestamp, {
      //       zone: 'Asia/Bangkok',
      //     });
      //     const formattedDateTime = timestampUTC7.toFormat('dd/MM/yyyy HH:mm'); // Match the format as per image

      //     const price =
      //       buffet.status === BuffetStatus.VOID || buffet.status === undefined
      //         ? -Math.abs(buffet.price)
      //         : buffet.price;

      //     totalBuffetPrice += price;

      //     const row = worksheet.addRow([
      //       formattedDateTime.split(' ')[0], // Date in 'dd/MM/yyyy'
      //       buffet.deviceName || '-', // Outlet/Station
      //       formattedDateTime || '-', // Order DateTime in 'dd/MM/yyyy HH:mm' format
      //       '-', // Receipt No. (not applicable for buffet logs)
      //       buffet.buffetNo, // Product Code (not applicable for buffet logs)
      //       '-',
      //       buffet.foodType || '-',
      //       'portion', // Unit (e.g., 'portion')
      //       '1', // Qty
      //       price, // Price
      //       price, // Sub Total                               // Category (not applicable for buffet logs)
      //       '-', // Seller (not applicable for buffet logs)
      //       '-',
      //       'สมาชิก', // Payment Type for buffet logs
      //       buffet.status || '-', // Status
      //       buffet.member.code || '-', // Code
      //       `${buffet.member.firstname} ${buffet.member.middlename || ''} ${buffet.member.lastname}` ||
      //         '-', // Name
      //       buffet.member.cardType || '-', // Type
      //     ]);

      //     row.eachCell((cell) => {
      //       cell.border = {
      //         top: { style: 'thin' },
      //         left: { style: 'thin' },
      //         bottom: { style: 'thin' },
      //         right: { style: 'thin' },
      //       };
      //     });
      //   });
      // }
    }

    // Calculate the grand total
    const grandTotal = totalSaleOrder + totalBuffetPrice;

    // Add a total row for sale orders and optionally buffet data
    const grandTotalRow = worksheet.addRow([
      'Total',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      grandTotal.toFixed(2),
    ]);
    worksheet.mergeCells(grandTotalRow.number, 1, grandTotalRow.number, 9);
    grandTotalRow.getCell(1).alignment = {
      horizontal: 'right',
      vertical: 'middle',
    };
    grandTotalRow.getCell(10).alignment = {
      horizontal: 'right',
      vertical: 'middle',
    };

    grandTotalRow.eachCell((cell) => {
      cell.font = { name: 'Arial', size: 9, bold: true };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFCCCCCC' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
    });

    // Generate buffer and return
    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }

  // async reportHistoryOrderMenu(
  //   startDate: Date,
  //   endDate: Date,
  //   memberIds: number[],
  // ) {
  //   const orders = await Order.find({
  //     select: {
  //       member: {
  //         id: true,
  //         code: true,
  //         firstname: true,
  //         middlename: true,
  //         lastname: true,
  //       },
  //       device: {
  //         id: true,
  //         code: true,
  //         name: true,
  //         branch: {
  //           id: true,
  //           code: true,
  //           name: true,
  //         },
  //       },
  //       orderItems: {
  //         id: true,
  //         quantity: true,
  //         price: true,
  //         total: true,
  //         product: {
  //           id: true,
  //           name: true,
  //         },
  //       },
  //     },
  //     where: {
  //       orderDate: Between(startDate, endDate),
  //       member: {
  //         id: In(memberIds),
  //       },
  //       orderStatus: In([OrderStatus.COMPLETE, OrderStatus.VOID]),
  //     },
  //     relations: {
  //       member: true,
  //       orderItems: { product: true },
  //       device: { branch: true },
  //     },
  //     order: {
  //       member: { code: 'ASC' },
  //       orderDate: 'ASC',
  //     },
  //   });

  //   const ordersGroupByMember = chain(orders)
  //     .groupBy(
  //       (order) =>
  //         `(${order.member?.code}) ${order.member?.firstname} (${order.member?.middlename}) ${order.member?.lastname}`,
  //     )
  //     .map((v, k) => ({
  //       member: k,
  //       outlets: chain(v)
  //         .groupBy((order) => order.device.branch.name)
  //         .map((v, k) => ({
  //           outlet: k,
  //           dates: chain(v)
  //             .groupBy((order) => datetime2string(order.orderDate).date)
  //             .map((v, k) => ({
  //               date: k,
  //               orders: v,
  //             }))
  //             .value(),
  //         }))
  //         .value(),
  //     }))
  //     .value();

  //   // const buffets = await Buffet.find({
  //   //   select: {
  //   //     member: {
  //   //       id: true,
  //   //       code: true,
  //   //       firstname: true,
  //   //       middlename: true,
  //   //       lastname: true,
  //   //     },
  //   //     device: {
  //   //       id: true,
  //   //       code: true,
  //   //       name: true,
  //   //       branch: {
  //   //         id: true,
  //   //         code: true,
  //   //         name: true,
  //   //       },
  //   //     },
  //   //   },
  //   //   where: {
  //   //     timestamp: Between(startDate, endDate),
  //   //     member: {
  //   //       id: In(memberIds),
  //   //     },
  //   //     // status: BuffetStatus.SUCCESS,
  //   //   },
  //   //   relations: {
  //   //     member: true,
  //   //     device: { branch: true },
  //   //   },
  //   //   order: {
  //   //     member: { code: 'ASC' },
  //   //     timestamp: 'ASC',
  //   //   },
  //   // });

  //   const buffetsGroupByMember = chain(buffets)
  //     .groupBy(
  //       (order) =>
  //         `(${order.member.code}) ${order.member.firstname} (${order.member.middlename}) ${order.member.lastname}`,
  //     )
  //     .map((v, k) => ({
  //       member: k,
  //       outlets: chain(v)
  //         .groupBy((order) => order.device?.branch.name)
  //         .map((v, k) => ({
  //           outlet: k,
  //           dates: chain(v)
  //             .groupBy((order) => datetime2string(order.timestamp).date)
  //             .map((v, k) => ({
  //               date: k,
  //               orders: v.map((e) => ({
  //                 orderNo: e.buffetNo,
  //                 orderItems: [
  //                   {
  //                     quantity: 1,
  //                     price: e.price,
  //                     total: e.price,
  //                     product: {
  //                       name: e.foodType,
  //                     },
  //                   },
  //                 ],
  //               })),
  //             }))
  //             .value(),
  //         }))
  //         .value(),
  //     }))
  //     .value();
  //   const mergedMap = {};

  //   ordersGroupByMember.forEach((item) => {
  //     if (!mergedMap[item.member]) {
  //       mergedMap[item.member] = { ...item };
  //     } else {
  //       mergedMap[item.member].outlets.push(...item.outlets);
  //     }
  //   });

  //   buffetsGroupByMember.forEach((item) => {
  //     if (!mergedMap[item.member]) {
  //       mergedMap[item.member] = { ...item };
  //     } else {
  //       mergedMap[item.member].outlets.push(...item.outlets);
  //     }
  //   });

  //   const mergedArray: {
  //     member: string;
  //     outlets: {
  //       outlet: string;
  //       dates: {
  //         date: string;
  //         orders: any[];
  //       }[];
  //     }[];
  //   }[] = Object.values(mergedMap);

  //   for (const arr of mergedArray) {
  //     const mergedOutletMap = {};

  //     arr.outlets.forEach((item) => {
  //       if (!mergedOutletMap[item.outlet]) {
  //         mergedOutletMap[item.outlet] = { ...item };
  //       } else {
  //         mergedOutletMap[item.outlet].dates.push(...item.dates);
  //       }
  //     });

  //     const mergedOutletArray: any[] = Object.values(mergedOutletMap);
  //     arr.outlets = mergedOutletArray;
  //   }

  //   for (const arr of mergedArray) {
  //     for (const outlet of arr['outlets']) {
  //       const mergedDateMap = {};

  //       outlet.dates.forEach((item) => {
  //         if (!mergedDateMap[item.date]) {
  //           mergedDateMap[item.date] = { ...item };
  //         } else {
  //           mergedDateMap[item.date].orders.push(...item.orders);
  //         }
  //       });

  //       const mergedDateArray: any[] = Object.values(mergedDateMap);
  //       outlet.dates = mergedDateArray;
  //     }
  //   }

  //   const BORDER: Partial<ExcelJS.Borders> = {
  //     top: { style: 'thin' },
  //     left: { style: 'thin' },
  //     bottom: { style: 'thin' },
  //     right: { style: 'thin' },
  //   };
  //   const FILL1: ExcelJS.Fill = {
  //     type: 'pattern',
  //     pattern: 'solid',
  //     bgColor: { argb: 'FFCCCCCC' },
  //     fgColor: { argb: 'FFCCCCCC' },
  //   };
  //   const FILL2: ExcelJS.Fill = {
  //     type: 'pattern',
  //     pattern: 'solid',
  //     bgColor: { argb: 'FF999999' },
  //     fgColor: { argb: 'FF999999' },
  //   };
  //   const FONT: Partial<ExcelJS.Font> = { name: 'Arial', size: 16, bold: true };

  //   const workbook = new Workbook();

  //   const worksheet = workbook.addWorksheet('RepLogCardItemSale', {
  //     views: [{ showGridLines: false }],
  //   });

  //   worksheet.columns = [
  //     { width: 10 },
  //     { width: 16 },
  //     { width: 12 },
  //     { width: 28 },
  //     { width: 8 },
  //     { width: 40 },
  //     { width: 10 },
  //     { width: 10 },
  //     { width: 10 },
  //   ];

  //   const startDateStr = datetime2string(startDate).date;
  //   const endDateStr = datetime2string(endDate).date;

  //   const header = worksheet.addRow(['รายงานประวัติการตัดจ่ายเมนูอาหาร']);
  //   header.font = FONT;
  //   const daterang = worksheet.addRow([
  //     'ช่วงวันที่',
  //     startDateStr,
  //     'ถึงวันที่',
  //     endDateStr,
  //   ]);
  //   daterang.font = FONT;

  //   worksheet.addRow([]);

  //   let grandTotal = 0;
  //   const grandDiscount = 0;
  //   let grandNet = 0;

  //   for (const order of mergedArray) {
  //     worksheet
  //       .addRow(['', 'เลขที่บัตร', order.member, '', '', '', '', '', ''])
  //       .eachCell((cell, colNumber) => {
  //         cell.fill = FILL2;
  //         cell.border = { top: { style: 'thin' }, bottom: { style: 'thin' } };
  //       });

  //     let memberSumTotal = 0;
  //     const memberSumDiscount = 0;
  //     let memberSumNet = 0;
  //     for (const outlet of order.outlets) {
  //       worksheet.addRow(['', 'Outlet (ร้านค้า)', outlet.outlet]);

  //       worksheet
  //         .addRow([
  //           'วันที่',
  //           'เลขที่ใบเสร็จ',
  //           'ลำดับ',
  //           'รายการเมนู',
  //           'จำนวน',
  //           'ราคา',
  //           'รวม',
  //           'ส่วนลด',
  //           'ยอดรวม',
  //         ])
  //         .eachCell((cell, colNum) => {
  //           cell.border = BORDER;
  //           cell.border = { top: { style: 'thin' }, bottom: { style: 'thin' } };
  //         });

  //       let outletSumTotal = 0;
  //       const outletSumDiscount = 0;
  //       let outletSumNet = 0;
  //       for (let i = 0; i < outlet.dates.length; i++) {
  //         const date = outlet.dates[i];

  //         let sumTotal = 0;
  //         const sumDiscount = 0;
  //         let sumNet = 0;

  //         for (let k = 0; k < date.orders.length; k++) {
  //           const order = date.orders[k];

  //           for (let j = 0; j < order.orderItems.length; j++) {
  //             const orderItem = order.orderItems[j];

  //             const row = [];

  //             if (i == 0 && k == 0 && j == 0) {
  //               row.push(date.date);
  //             } else {
  //               row.push('');
  //             }

  //             if (j == 0) {
  //               row.push(order.orderNo);
  //             } else {
  //               row.push('');
  //             }

  //             const orderItemTotal = orderItem.quantity * orderItem.price;

  //             row.push(j + 1);
  //             row.push(orderItem.product.name);
  //             row.push(orderItem.quantity);
  //             row.push(orderItem.price);
  //             row.push(orderItem.total);
  //             row.push(0);
  //             row.push(orderItemTotal);

  //             worksheet.addRow(row);

  //             sumTotal += orderItem.total;
  //             sumNet += orderItemTotal;

  //             outletSumTotal += orderItem.total;
  //             outletSumNet += orderItemTotal;

  //             memberSumTotal += orderItem.total;
  //             memberSumNet += orderItemTotal;

  //             grandTotal += orderItem.total;
  //             grandNet += orderItemTotal;
  //           }
  //         }

  //         worksheet
  //           .addRow([
  //             '',
  //             '',
  //             '',
  //             '',
  //             '',
  //             `รวมยอดของวันที่  # ${date.date}`,
  //             sumTotal,
  //             sumDiscount,
  //             sumNet,
  //           ])
  //           .eachCell((cell, colNumber) => {
  //             cell.fill = FILL1;
  //             cell.border = {
  //               top: { style: 'thin' },
  //               bottom: { style: 'thin' },
  //             };
  //           });
  //       }
  //       worksheet
  //         .addRow([
  //           '',
  //           '',
  //           '',
  //           '',
  //           '',
  //           `รวมยอดของ # ${outlet.outlet}`,
  //           outletSumTotal,
  //           outletSumDiscount,
  //           outletSumNet,
  //         ])
  //         .eachCell((cell, colNumber) => {
  //           cell.fill = FILL1;
  //           cell.border = { top: { style: 'thin' }, bottom: { style: 'thin' } };
  //         });
  //     }

  //     worksheet
  //       .addRow([
  //         '',
  //         '',
  //         '',
  //         '',
  //         '',
  //         `รวมค่าใช้จ่ายของ ${order.member}`,
  //         memberSumTotal,
  //         memberSumDiscount,
  //         memberSumNet,
  //       ])
  //       .eachCell((cell, colNumber) => {
  //         cell.fill = FILL1;
  //         cell.border = { top: { style: 'thin' }, bottom: { style: 'thin' } };
  //       });
  //   }

  //   worksheet
  //     .addRow([
  //       '',
  //       '',
  //       '',
  //       '',
  //       '',
  //       `รวมค่าใช้จ่ายทั้งหมด`,
  //       grandTotal,
  //       grandDiscount,
  //       grandNet,
  //     ])
  //     .eachCell((cell, colNumber) => {
  //       cell.fill = FILL1;
  //       cell.border = { top: { style: 'thin' }, bottom: { style: 'thin' } };
  //     });

  //   return workbook.xlsx.writeBuffer();
  // }

  async reportSaleSummary(
    startDate: Date,
    endDate: Date,
    deviceId: number,
    userId: any,
  ) {
    const startDateFormatted: string = DateTime.fromJSDate(startDate, {
      zone: 'Asia/Bangkok',
    }).toFormat('dd/MM/yyyy');
    const endDateFormatted: string = DateTime.fromJSDate(endDate, {
      zone: 'Asia/Bangkok',
    }).toFormat('dd/MM/yyyy');
    const now: string = DateTime.now().toFormat('dd/MM/yyyy HH:mm:ss');

    const salesSummary = await this.orderRepository
      .createQueryBuilder('o')
      .select([
        'd.name AS deviceName',
        "SUM(CASE WHEN o.order_status = 'complete' THEN ABS(o.grand_total) ELSE 0 END) AS paid",
        "ABS(SUM(CASE WHEN o.order_status = 'void' THEN o.grand_total ELSE 0 END)) AS void",
        "SUM(CASE WHEN o.order_status = 'complete' THEN ABS(o.grand_total) ELSE 0 END) - ABS(SUM(CASE WHEN o.order_status = 'void' THEN o.grand_total ELSE 0 END)) AS total",
      ])
      .innerJoin('o.device', 'd') // เชื่อมกับตาราง device
      .where('o.order_date >= :startDate', { startDate })
      .andWhere('o.order_date <= :endDate', { endDate })
      .andWhere('d.id = :deviceId', { deviceId }) // กรองข้อมูลตาม deviceId
      .groupBy('d.name')
      .getRawOne();

    const categorySummary = await this.orderItemRepository
      .createQueryBuilder('oi')
      .select('c.name', 'category_name')
      .addSelect('d.name', 'device_name')
      .addSelect('SUM(oi.total)', 'total_sales')
      .innerJoin(Product, 'p', 'oi.product_id = p.id')
      .leftJoin(Category, 'c', 'p.category_id = c.id')
      .leftJoin(Order, 'o', 'oi.order_id = o.id')
      .leftJoin(Device, 'd', 'o.device_id = d.id')
      .where('o.order_status = :status', { status: 'complete' })
      .andWhere('o.device_id = :deviceId', { deviceId: deviceId })
      .andWhere('o.order_date BETWEEN :startDate AND :endDate', {
        startDate: startDate,
        endDate: endDate,
      })
      .groupBy('c.name')
      .addGroupBy('d.name')
      .orderBy('total_sales', 'DESC')
      .getRawMany();

    // const orders = await this.orderRepository
    //   .createQueryBuilder('order')
    //   .innerJoin('order.orderPayments', 'order_payment')
    //   .innerJoin('order_payment.paymentMethod', 'payment_method')
    //   .innerJoin('order.orderItems', 'order_item')
    //   .select([
    //     'payment_method.id',
    //     'payment_method.name',
    //     'SUM(order_item.total) AS totalAmount'
    //   ])
    //   .where('order.orderDate >= :startDate', { startDate: startDate })
    //   .andWhere('order.orderDate <= :endDate', { endDate: endDate })
    //   .andWhere('order.orderStatus = :status', { status: 'complete' })
    //   .andWhere('order_payment.status = :paymentStatus', { paymentStatus: 'success' })
    //   .andWhere('order.device_id = :deviceId', { deviceId: deviceId }) // เพิ่มเงื่อนไข device_id
    //   .andWhere('payment_method.id IN (:...paymentMethodIds)', { paymentMethodIds: [1, 2, 3, 4] })
    //   .groupBy('payment_method.id')
    //   .addGroupBy('payment_method.name')
    //   .getRawMany();

    // const formattedData = orders.map(item => ({
    //   ...item,
    //   totalamount: Number(item.totalamount)
    // }));

    const orders = await this.orderRepository
      .createQueryBuilder('order')
      .innerJoin('order.orderPayments', 'order_payment')
      .innerJoin('order_payment.paymentMethod', 'payment_method')
      .innerJoin('order.orderItems', 'order_item')
      .select([
        'payment_method.id AS payment_method_id',
        'payment_method.name AS payment_method_name',
        "SUM(CASE WHEN order.orderStatus = 'complete' THEN order_item.total ELSE 0 END) AS total_complete",
        "SUM(CASE WHEN order.orderStatus = 'void' THEN order_item.total ELSE 0 END) AS total_void",
        "SUM(CASE WHEN order.orderStatus = 'complete' THEN ABS(order_item.total) ELSE 0 END) - SUM(CASE WHEN order.orderStatus = 'void' THEN ABS(order_item.total) ELSE 0 END) AS totalamount",
      ])
      .where('order.orderDate >= :startDate', { startDate })
      .andWhere('order.orderDate <= :endDate', { endDate })
      .andWhere('order_payment.status = :paymentStatus', {
        paymentStatus: 'success',
      })
      .andWhere('order.device_id = :deviceId', { deviceId })
      .andWhere('payment_method.id IN (:...paymentMethodIds)', {
        paymentMethodIds: [1, 2, 3, 4],
      })
      .groupBy('payment_method.id')
      .addGroupBy('payment_method.name')
      .getRawMany();

    const formattedData = orders.map((item) => ({
      payment_method_id: Number(item.payment_method_id),
      payment_method_name: item.payment_method_name,
      totalamount: +item.total_complete - Number(Math.abs(item.total_void)), // ผลรวมที่เป็น complete - void
    }));

    const result = {
      reportTitle: 'Sales Summary All',
      dateRange: {
        startDate: startDateFormatted,
        endDate: endDateFormatted,
      },

      outlet: salesSummary.devicename,
      reportPulledBy: userId,
      salesSummary: {
        paid: salesSummary.paid,
        void1: salesSummary.void,
        total: salesSummary.total,
      },
      salesByCategory: categorySummary.map((category) => ({
        category: category.category_name,
        amount: parseFloat(category.total_sales),
      })),
      totalByCategory: +salesSummary.total,
      salesByPaymentMethod: formattedData,
      totalByPaymentMethod: +salesSummary.total,
      printedTime: now,
    };
    return result;
  }

  async Top10BestSellersReport(
    startDate: Date,
    endDate: Date,
    branchCodes?: string[],
  ) {
    // Fetch order items within the date range
    const query = this.orderItemRepository
      .createQueryBuilder('orderItem')
      .leftJoinAndSelect('orderItem.order', 'o')
      .leftJoinAndSelect('orderItem.product', 'p')
      .leftJoinAndSelect('o.branch', 'b')
      .where('o.created_at BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .andWhere('o.order_status = :status', { status: OrderStatus.COMPLETE });

    if (branchCodes && branchCodes.length > 0) {
      query.andWhere('b.code IN (:...branchCodes)', { branchCodes });
    }

    const orderItems = await query.getMany();

    // Aggregate sales data by product and branch
    const salesData: {
      [productId: number]: {
        productId: number;
        quantity: number;
        total: number;
        branchNames: string[];
      };
    } = {};

    for (const item of orderItems) {
      if (!salesData[item.product.id]) {
        salesData[item.product.id] = {
          productId: item.product.id,
          quantity: 0,
          total: 0,
          branchNames: [],
        };
      }
      salesData[item.product.id].quantity += item.quantity;
      salesData[item.product.id].total += item.quantity * item.price;
      const branchName = item.order.branch.name; // Assuming branch name is available in `item.order.branch.name`
      if (!salesData[item.product.id].branchNames.includes(branchName)) {
        salesData[item.product.id].branchNames.push(branchName);
      }
    }

    // Convert sales data to array and sort
    const sortedSalesData = Object.values(salesData)
      .sort((a, b) => b.total - a.total)
      .slice(0, 10); // Top 10

    const productIds = Array.from(
      new Set(sortedSalesData.map((data) => data.productId)),
    );
    const products = await this.productRepository.findByIds(productIds);

    // Create Excel file
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Top 10 Best Sellers');
    // Define columns for the worksheet
    worksheet.columns = [
      { header: 'Product Name', key: 'name', width: 30 },
      { header: 'Quantity Sold', key: 'quantity', width: 20 },
      { header: 'Total Sales', key: 'total', width: 20 },
      { header: 'Branch Names', key: 'branchNames', width: 30 },
      {
        header: 'Start Date',
        key: 'startDate',
        width: 20,
        style: { numFmt: 'yyyy-mm-dd' },
      },
      {
        header: 'End Date',
        key: 'endDate',
        width: 20,
        style: { numFmt: 'yyyy-mm-dd' },
      },
    ];

    // Style the header row with a grey background
    worksheet.getRow(1).eachCell({ includeEmpty: true }, (cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'D3D3D3' }, // Light grey color
      };
      cell.font = {
        bold: true,
      };
    });

    // Add rows with data
    for (const data of sortedSalesData) {
      const product = products.find((p) => p.id === data.productId);
      worksheet.addRow({
        name: product?.name || 'Unknown',
        quantity: data.quantity,
        total: data.total.toFixed(2),
        branchNames: data.branchNames.join(', '),
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate.toISOString().split('T')[0],
      });
    }

    // Write to buffer
    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async summaryExpensesExcel(startDate: Date, endDate: Date) {
    const findmember = await this.orderRepository.find({
      where: {
        orderDate: Between(startDate, endDate),
        orderStatus: In([OrderStatus.COMPLETE, OrderStatus.VOID]),
      },
      relations: { member: true },
    });
    if (!findmember) {
      throw new BadRequestException('Data not found');
    }

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Summary Expenses');

    // Add headers
    worksheet.addRow(['Summary Expenses']);
    worksheet.addRow([
      'Date',
      `${startDate.toISOString().slice(0, 10)}`,
      'To',
      `${endDate.toISOString().slice(0, 10)}`,
    ]);

    // Initialize total counters
    let totalCredit = 0;
    let memberCount = 0;

    for (const item of findmember) {
      worksheet.addRow([
        'first-name',
        'last-name',
        'emp-no',
        'type',
        'card-sn',
        'time-stamp',
        'activity-order',
        'credit',
        'Total',
      ]);

      // Add data rows
      findmember.forEach((findmember) => {
        const row = 0;
        if (row < 2) {
          worksheet.addRow([
            findmember.member.firstname,
            findmember.member.lastname,
            findmember.member.code,
            findmember.member.cardType,
            findmember.member.sn,
            findmember.orderDate,
            findmember.orderNo,
            findmember.member.credit,
          ]);
        } else {
          worksheet.addRow([
            '',
            '',
            '',
            '',
            '',
            '',
            findmember.orderDate,
            findmember.orderNo,
            findmember.member.credit,
          ]);
        }
      });
      worksheet.addRow(['', '', '', '', '', '', '', '', item.member.credit]);
      totalCredit += item.member.credit;
      memberCount++;
    }
    // Add total row at the end
    worksheet.addRow([
      '',
      '',
      '',
      '',
      '',
      'Start Date',
      'End Date',
      'Total Members:',
      'Total Credit:',
    ]);
    worksheet.addRow([
      '',
      '',
      '',
      '',
      '',
      startDate,
      endDate,
      memberCount,
      totalCredit,
    ]);

    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(2).font = { bold: true };

    // Convert workbook to buffer
    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async generateSalesReport(
    startDate: Date,
    endDate: Date,
    deviceIds?: number[],
    categoryIds?: number[],
  ) {
    const startDateTime = DateTime.fromJSDate(startDate)
      .set({ hour: 0, minute: 0, second: 0 })
      .toJSDate();
    const endDateTime = DateTime.fromJSDate(endDate)
      .set({ hour: 23, minute: 59, second: 59 })
      .toJSDate();

    const query = this.orderItemRepository
      .createQueryBuilder('orderItem')
      .leftJoinAndSelect('orderItem.order', 'o')
      .leftJoinAndSelect('orderItem.product', 'p')
      .leftJoinAndSelect('p.category', 'c') // Join with Category entity
      .leftJoinAndSelect('o.branch', 'b')
      .where('o.createdAt BETWEEN :startDate AND :endDate', {
        startDate: startDateTime,
        endDate: endDateTime,
      });

    // if (branchCodes && branchCodes.length > 0) {
    //   query.andWhere('b.code IN (:...branchCodes)', { branchCodes });
    // }

    if (deviceIds && deviceIds.length > 0) {
      query.andWhere('o.device_id IN (:...deviceIds)', { deviceIds });
    }

    if (categoryIds && categoryIds.length > 0) {
      query.andWhere('c.id IN (:...categoryIds)', { categoryIds });
    }

    // Log the query to verify it is correct
    console.log('Generated SQL Query:', query.getSql());

    const orderItems = await query.getMany();

    // Log the orderItems to inspect data
    console.log('Order Items:', JSON.stringify(orderItems, null, 2));

    const salesData: {
      [productId: number]: {
        productId: number;
        code: string;
        name: string;
        categoryName: string;
        quantity: number;
        price: number;
        total: number;
      };
    } = {};

    for (const item of orderItems) {
      if (!item.product || !item.product.category) {
        console.error('Product or Category is undefined for orderItem:', item);
        continue;
      }

      const productId = item.product.id;
      if (!salesData[productId]) {
        salesData[productId] = {
          productId: productId,
          code: item.product.code,
          name: item.product.name,
          categoryName: item.product.category.name, // Use categoryName instead of categoryCode
          quantity: 0,
          price: item.price,
          total: 0,
        };
      }
      salesData[productId].quantity += item.quantity;
      salesData[productId].total += item.quantity * item.price;
    }

    const sortedSalesData = Object.values(salesData);

    // Create Excel file
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Sales Report');
    worksheet.columns = [
      { header: 'ลำดับ (No.)', key: 'productId', width: 15 },
      { header: 'รหัสสินค้า (Product Code)', key: 'code', width: 30 },
      { header: 'ชื่อสินค้า (Product Name)', key: 'name', width: 30 },
      { header: 'หมวดหมู่ สินค้า (Category)', key: 'categoryName', width: 30 }, // Include only Category Name
      { header: 'จำนวนขาย (Quantity Sold)', key: 'quantity', width: 30 },
      { header: 'ราคา ต่อ หน่วย(Units Price)', key: 'price', width: 30 },
      { header: 'ราคา รวม (Total Sales)', key: 'total', width: 30 },
    ];

    worksheet.getRow(1).eachCell({ includeEmpty: true }, (cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'D3D3D3' },
      };
      cell.font = {
        bold: true,
      };
    });

    for (const data of sortedSalesData) {
      worksheet.addRow({
        productId: data.productId,
        code: data.code,
        name: data.name,
        categoryName: data.categoryName, // Use categoryName instead of categoryCode
        quantity: data.quantity,
        price: data.price.toFixed(2),
        total: data.total.toFixed(2),
      });
    }

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  // async reportCreditChange(
  //   startDate: Date,
  //   endDate: Date,
  //   memberIds?: number[],
  // ) {
  //   const transactions = await MemberCreditChange.find({
  //     select: {
  //       createdAt: true,
  //       Newcredit: true,
  //       Oldcredit: true,
  //       Type: true,
  //       member: {
  //         code: true,
  //         firstname: true,
  //         middlename: true,
  //         lastname: true,
  //         limitcredit: true,
  //       },
  //     },
  //     where: {
  //       createdAt: Between(startDate, endDate),
  //       member: {
  //         id: In(memberIds),
  //       },
  //     },
  //     relations: {
  //       member: { grade: true },
  //     },
  //     order: {
  //       member: { code: 'ASC' },
  //       createdAt: 'ASC',
  //     },
  //   });

  //   // console.log(transactions);

  //   // Create Excel file
  //   const workbook = new Workbook();
  //   const worksheet = workbook.addWorksheet('Sales Report');
  //   worksheet.columns = [
  //     { header: 'วันที่/เวลา', key: 'createdAt', width: 30 },
  //     { header: 'รหัสนักเรียน', key: 'code', width: 30 },
  //     { header: 'ชื่อ', key: 'firstname', width: 30 },
  //     { header: 'ชื่อกลาง', key: 'middlename', width: 30 },
  //     { header: 'นามสกุล', key: 'lastname', width: 30 },
  //     { header: 'Grade', key: 'grade', width: 30 },
  //     { header: 'Grade LimitCredit', key: 'gradecredit', width: 30 },
  //     { header: 'Old Ceredit', key: 'oldCredit', width: 30 },
  //     { header: 'New Credit', key: 'newCredit', width: 30 },
  //     { header: 'ประเภท', key: 'type', width: 30 },
  //   ];

  //   worksheet.getRow(1).eachCell({ includeEmpty: true }, (cell) => {
  //     cell.fill = {
  //       type: 'pattern',
  //       pattern: 'solid',
  //       fgColor: { argb: 'D3D3D3' },
  //     };
  //     cell.font = {
  //       bold: true,
  //     };
  //   });

  //   for (const data of transactions) {
  //     worksheet.addRow({
  //       createdAt: DateTime.fromJSDate(data.createdAt, {
  //         zone: 'Asia/Bangkok',
  //       }).toFormat('dd/MM/yyyy HH:mm:ss'),
  //       code: data?.member?.code,
  //       firstname: data?.member?.firstname,
  //       middlename: data?.member?.middlename,
  //       lastname: data?.member?.lastname,
  //       grade: data?.member?.grade?.name,
  //       gradecredit: data?.member?.grade?.limitcredit,
  //       oldCredit: data?.Oldcredit,
  //       newCredit: data?.Newcredit,
  //       type: data.Type,
  //     });
  //   }
  //   const buffer = await workbook.xlsx.writeBuffer();
  //   return buffer;
  // }

  async reportAuditlog(startDate: Date, endDate: Date, userId?: number) {
    const checkuser = await User.findOneBy({ id: userId });
    if (!checkuser) {
      throw new BadRequestException('User not found');
    }
    const auditlog = await AuditLog.find({
      where: {
        createdAt: Between(startDate, endDate),
        ...(userId ? { userId } : {}),
      },
    });

    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Sales Report');
    worksheet.columns = [
      { header: 'ลำดับ', key: 'No', width: 5 },
      { header: 'Time-stamp', key: 'timestamp', width: 30 },
      { header: 'กิจกรรม', key: 'Action', width: 30 },
      { header: 'คำอธิบาย', key: 'description', width: 60 },
    ];

    worksheet.getRow(1).eachCell({ includeEmpty: true }, (cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'D3D3D3' },
      };
      cell.font = {
        bold: true,
      };
    });
    let gennum = 1;

    for (const data of auditlog) {
      worksheet.addRow({
        No: gennum,
        timestamp: data?.timestamp.toLocaleString('en-GB', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        }),
        Action: data?.action,
        description: data?.description,
      });
      gennum++;
    }
    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async searchDevicessumeryExcel(
    startDate: Date,
    endDate: Date,
    deviceIds: number[],
  ) {
    const nowDate = DateTime.now().setZone('Asia/Bangkok').toLocaleString();
    const showDevices = await this.orderRepository.find({
      where: {
        orderDate: Between(startDate, endDate),
        orderStatus: In([OrderStatus.COMPLETE, OrderStatus.VOID]),
        device: {
          id: In(deviceIds),
        },
      },
      relations: {
        device: true,
        orderPayment: { paymentMethod: true },
        user: true,
      },
    });

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Device Summary');

    const fixedPaymentMethods = ['สมาชิก', 'พร้อมเพย์', 'แม่มณี'];

    worksheet.columns = [
      { header: 'No.', key: 'serialNo', width: 5 },
      { header: 'ชื่อร้านค้า', key: 'deviceName', width: 20 },
      { header: 'ชื่อผู้ขาย', key: 'sellerName', width: 15 },
      ...fixedPaymentMethods.map((method) => ({
        header: `รวมราคาขาย ${method}`,
        key: method,
        width: 20,
      })),
      { header: 'รวมยอดการขาย', key: 'totalGrandTotal', width: 15 },
    ];

    worksheet.mergeCells('A1:G1');
    const titleRow = worksheet.getRow(1);
    titleRow.getCell(1).value = 'รายงานสรุปยอดการขายแยกตามร้าน';
    titleRow.font = { size: 16, bold: true };
    titleRow.alignment = { vertical: 'middle', horizontal: 'center' };
    titleRow.height = 30;

    titleRow.getCell(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'D9D9D9' },
    };

    worksheet.mergeCells('A2:G2');
    const subtitleRow = worksheet.getRow(2);
    subtitleRow.getCell(1).value =
      `รายงานระหว่างวันที่   ${startDate.toLocaleDateString()}   ถึง   ${endDate.toLocaleDateString()}     พิมพ์วันที่ ${nowDate}`;
    subtitleRow.font = { size: 12 };
    subtitleRow.alignment = { vertical: 'middle', horizontal: 'left' };
    subtitleRow.height = 20;

    worksheet.addRow([]); // Add an empty row for spacing

    const headerRow = worksheet.addRow([
      'No.',
      'ชื่อร้านค้า',
      'ชื่อผู้ขาย',
      ...fixedPaymentMethods.map((method) => `รวมราคาขาย ${method}`),
      'รวมยอดการขาย',
    ]);

    headerRow.font = { bold: true };
    headerRow.alignment = { vertical: 'middle', horizontal: 'center' };

    [
      'A',
      'B',
      'C',
      ...fixedPaymentMethods.map((_, index) => String.fromCharCode(68 + index)),
      'G',
    ].forEach((col) => {
      headerRow.getCell(col).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'D9D9D9' },
      };
    });

    const deviceSummary = new Map<
      string,
      {
        totalOrders: number;
        totalPaid: number;
        totalGrandTotal: number;
        paymentMethods: Map<string, number>;
        sellerName: string;
      }
    >();

    for (const order of showDevices) {
      const deviceName = order.device ? order.device.name : 'Unknown Device';
      const sellerName = order.user
        ? `${order.user.firstName} ${order.user.lastName}`
        : 'Unknown Seller';

      const existingSummary = deviceSummary.get(deviceName) || {
        totalOrders: 0,
        totalPaid: 0,
        totalGrandTotal: 0,
        paymentMethods: new Map<string, number>(),
        sellerName,
      };

      existingSummary.totalOrders += 1;
      existingSummary.totalPaid += order.paid || 0;
      existingSummary.totalGrandTotal += order.grandTotal || 0;
      existingSummary.sellerName = sellerName;

      if (order.orderPayment) {
        if (order.orderPayment) {
          const methodName = order.orderPayment.paymentMethod.name || 'Unknown Method';
          existingSummary.paymentMethods.set(
            methodName,
            (existingSummary.paymentMethods.get(methodName) || 0) +
              (order.grandTotal || 0),
          );
        }
      }

      deviceSummary.set(deviceName, existingSummary);
    }

    // Convert deviceSummary to an array and sort by device name
    const sortedDeviceSummary = Array.from(deviceSummary.entries()).sort(
      ([nameA], [nameB]) => nameA.localeCompare(nameB),
    );

    let totalGrandTotalSummary = 0;
    const paymentMethodTotals: Record<string, number> = {};
    fixedPaymentMethods.forEach((method) => {
      paymentMethodTotals[method] = 0;
    });

    let serialNo = 1;
    for (const [deviceName, summary] of sortedDeviceSummary) {
      const row: Record<string, any> = {
        serialNo,
        deviceName,
        sellerName: summary.sellerName,
        totalGrandTotal: summary.totalGrandTotal,
      };

      for (const method of fixedPaymentMethods) {
        const methodTotal = summary.paymentMethods.get(method) || 0;
        row[method] = methodTotal === 0 ? '-' : methodTotal;
        paymentMethodTotals[method] += methodTotal;
      }

      totalGrandTotalSummary += summary.totalGrandTotal;
      const rowObj = worksheet.addRow(row);

      // Apply number format with commas for numbers
      ['D', 'E', 'F', 'G'].forEach((col) => {
        if (rowObj.getCell(col).value !== '-') {
          rowObj.getCell(col).numFmt = '#,##0';
        }
      });

      serialNo++;
    }

    const summaryRow = {
      serialNo: '',
      deviceName: 'รวมทั้งหมด',
      sellerName: '',
      totalGrandTotal: totalGrandTotalSummary,
    };

    fixedPaymentMethods.forEach((method) => {
      summaryRow[method] =
        paymentMethodTotals[method] === 0 ? '-' : paymentMethodTotals[method];
    });

    const summaryRowObj = worksheet.addRow(summaryRow);
    worksheet.mergeCells(`A${summaryRowObj.number}:C${summaryRowObj.number}`);
    summaryRowObj.getCell('A').value = 'รวมทั้งหมด';
    summaryRowObj.getCell('A').alignment = {
      vertical: 'middle',
      horizontal: 'center',
    };

    [
      'A',
      'B',
      'C',
      ...fixedPaymentMethods.map((_, index) => String.fromCharCode(68 + index)),
      'G',
    ].forEach((col) => {
      summaryRowObj.getCell(col).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'D9D9D9' },
      };
      summaryRowObj.getCell(col).font = { bold: true };
      summaryRowObj.getCell(col).alignment = {
        vertical: 'middle',
        horizontal: 'center',
      };
      if (summaryRowObj.getCell(col).value !== '-' && col !== 'A') {
        summaryRowObj.getCell(col).numFmt = '#,##0';
      }
    });

    worksheet.eachRow((row, rowNumber) => {
      row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        if (colNumber <= 7) {
          cell.alignment = { vertical: 'middle', horizontal: 'center' };
          cell.border = {
            top: { style: 'medium' },
            left: { style: 'medium' },
            bottom: { style: 'medium' },
            right: { style: 'medium' },
          };
          if (rowNumber > 4 && rowNumber !== summaryRowObj.number) {
            if (typeof cell.value === 'number') {
              cell.numFmt = '#,##0';
            }
          }
        }
      });
    });

    return workbook.xlsx.writeBuffer();
  }
}
