import { <PERSON>, Get, Post, Body, Patch, Param, Delete, ParseIntPipe, Put, HttpStatus, HttpCode } from '@nestjs/common';
import { VENDOR_PAGINATION_CONFIG, VendorService } from './vendor.service';
import { CreateVendorDto } from './dto/create-vendor-dto';
import { UpdateVendorDto } from './dto/update-vendor-dto';
import { ApiTags } from '@nestjs/swagger';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';

@Controller('vendor')
@ApiTags('ผู้จำหน่าย')
export class VendorController {
    constructor(private readonly vendorService: VendorService) { }

    @Get('datatables')
    @HttpCode(HttpStatus.OK)
    @ApiPaginationQuery(VENDOR_PAGINATION_CONFIG)
    datatables(@Paginate() query: PaginateQuery) {
        return this.vendorService.datatables(query);
    }

    @Get('generate-vendor-code')
    generateVendorCode() {
        return this.vendorService.generateVendorCode();
    }

    @Post()
    create(@Body() createVendorDto: CreateVendorDto) {
        return this.vendorService.create(createVendorDto);
    }

    @Get()
    findAll() {
        return this.vendorService.findAll();
    }

    @Get(':id')
    findOne(@Param('id') id: string) {
        return this.vendorService.findOne(+id);
    }

    @Put(':id')
    update(
        @Param('id') id: string,
        @Body() updateVendorDto: UpdateVendorDto
    ) {
        return this.vendorService.update(+id, updateVendorDto);
    }

    @Delete(':id')
    remove(@Param('id') id: string) {
        return this.vendorService.remove(+id);
    }

    @Get(':id/receipts')
    @HttpCode(HttpStatus.OK)
    getVendorReceipts(@Param('id') id: string) {
        return this.vendorService.findOne(+id);
    }
}
