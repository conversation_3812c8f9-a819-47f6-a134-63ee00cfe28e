import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Vendor } from './entities/vendor.entity';
import { Like, Not, Repository } from 'typeorm';
import { CreateVendorDto } from './dto/create-vendor-dto';
import { UpdateVendorDto } from './dto/update-vendor-dto';
import { DateTime } from 'luxon';
import { paginate, PaginateConfig, Paginated, PaginateQuery } from 'nestjs-paginate';

export const VENDOR_PAGINATION_CONFIG: PaginateConfig<Vendor> = {
    sortableColumns: ['id', 'code', 'name', 'phone'],
    searchableColumns: ['code', 'name', 'phone'],
    select: ['id', 'code', 'name', 'phone', 'address', 'createdAt'],
};

@Injectable()
export class VendorService {
    constructor(
        @InjectRepository(Vendor)
        private vendorRepository: Repository<Vendor>,
    ) { }

    async generateVendorCode(): Promise<{ code: string }> {
        const lastVendor = await this.vendorRepository.find({
            order: { id: 'DESC' },
            take: 1
        });

        if (!lastVendor.length || !lastVendor[0].code) {
            return { code: 'V0001' };
        }

        const lastNumber = parseInt(lastVendor[0].code.slice(1), 10);
        const nextNumber = lastNumber + 1;

        return { code: 'V' + nextNumber.toString().padStart(4, '0') };
    }

    async create(createVendorDto: CreateVendorDto): Promise<Vendor> {
        const isExist = await this.vendorRepository.findOne({
            where: {
                code: createVendorDto.code
            },
        });

        if (isExist) {
            throw new BadRequestException('Vendor code already exists');
        }

        const vendor = this.vendorRepository.create(createVendorDto);
        return await this.vendorRepository.save(vendor);
    }

    async findOne(id: number) {
        const vendor = await this.vendorRepository.findOne({
            where: { id },
            relations: { products: true, receipts: true }
        });
        if (!vendor) {
            throw new NotFoundException('Vendor not found');
        }
        return vendor;
    }

    async findAll() {
        return await this.vendorRepository.find();
    }

    async update(id: number, updateVendorDto: UpdateVendorDto): Promise<Vendor> {
        const vendor = await this.findOne(id);

        const duplicate = await this.vendorRepository.findOne({
            where: {
                code: updateVendorDto.code,
                id: Not(id),
            },
        });

        if (duplicate) {
            throw new BadRequestException('Vendor code already exists');
        }

        const updated = Object.assign(vendor, updateVendorDto);
        return await this.vendorRepository.save(updated);
    }

    async remove(id: number) {
        const vendor = await this.findOne(id);
        const date = DateTime.now().toFormat('yyyy-MM-dd');

        await this.vendorRepository.update(id, { code: `${date}-${vendor.code}` });
        await this.vendorRepository.softDelete(id);
    }

    async datatables(query: PaginateQuery): Promise<Paginated<Vendor>> {
        return paginate(query, this.vendorRepository, VENDOR_PAGINATION_CONFIG);
    }

}
