import { Exclude, Expose } from 'class-transformer';
import { CustomBaseEntity } from '../../common/entities/custom-base.entity';
import { Role } from '../../role/entities/role.entity';
import {
    Column,
    Entity,
    Index,
    JoinColumn,
    JoinTable,
    ManyToMany,
    ManyToOne,
    OneToMany,
    Unique,
} from 'typeorm';
import { Shift } from '../../shift/entities/shift.entity';
import { Branch } from '../../branch/entities/branch.entity';
import { UserAtthem } from './user-atthem.entity';
import { AuditLog } from '../../auditlog/entities/auditlog.entity';
import { Order } from '../../order/entities/order.entity';
import { Store } from '../../store/entities/store.entity';

@Entity()
@Unique(['code', 'store'])
export class User extends CustomBaseEntity {
    @Column()
    @Index({ unique: true })
    username: string;

    @Column({ unique: true, nullable: true })
    email: string;

    @Column()
    @Exclude()
    password: string;

    @Column()
    @Index()
    code: string;

    @Column({ name: 'first_name' })
    firstName: string;

    @Column({ name: 'last_name' })
    lastName: string;

    @Column({ name: 'phone_number', nullable: true, length: 10 })
    phoneNumber: string;

    @Column({ name: 'is_active', default: true })
    isActive: boolean;

    @Column({ default: false })
    confirmed: boolean;

    @ManyToOne(() => Role, (_) => _.users)
    role: Role;

    @Exclude()
    @Column({ name: 'refresh_token', nullable: true })
    refreshToken: string;

    @OneToMany(() => Shift, (_) => _.user)
    shifts: Shift[];

    @ManyToOne(() => Store, (_) => _.users)
    store: Store;

    @ManyToMany(() => Branch, (_) => _.users)
    branchs: Branch[];

    @OneToMany(() => UserAtthem, (userAtthem) => userAtthem.user)
    userAtthems: UserAtthem[];

    @Expose()
    get fullName(): string {
        return `${this.firstName} ${this.lastName}`;
    }

    @OneToMany(() => Order, (_) => _.user)
    orders: Order[];

    @OneToMany(() => AuditLog, (_) => _.user)
    auditLogs: AuditLog[];
}
