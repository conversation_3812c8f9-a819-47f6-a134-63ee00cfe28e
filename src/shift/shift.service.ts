import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateShiftDto } from './dto/create-shift.dto';
import { UpdateShiftDto } from './dto/update-shift.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Shift, ShiftStatus } from './entities/shift.entity';
import {
  PaginateConfig,
  PaginateQuery,
  Paginated,
  paginate,
} from 'nestjs-paginate';
import { DateTime } from 'luxon';

export const SHIFT_PAGINATION_CONFIG: PaginateConfig<Shift> = {
  sortableColumns: ['id', 'createdAt'],
  searchableColumns: ['id'],
  // filterableColumns: {
  //   type: true,
  // },
  relativePath: true,
  relations: {
    user: true,
    store: true,
    device: true,
  }
};

@Injectable()
export class ShiftService {
  constructor(
    @InjectRepository(Shift)
    private shiftRepository: Repository<Shift>,
  ) { }

  create(user: any, createShiftDto: CreateShiftDto) {
    const userId = user['sub'];
    const storeId = user['storeId'];

    const shift = this.shiftRepository.create({
      ...createShiftDto,
      date: DateTime.local().toJSDate(),
      startShift: DateTime.local().toJSDate(),
      status: ShiftStatus.OPEN,
      user: {
        id: userId,
      },
      store: {
        id: storeId,
      },
      device: {
        id: createShiftDto.deviceId,
      },
    });

    return this.shiftRepository.save(shift);
  }

  async findCurrentShift(user: any, deviceId: number) {
    const storeId = user['storeId'];

    const shift = await this.shiftRepository.findOne({
      where: {
        store: { id: storeId },
        status: ShiftStatus.OPEN,
        device: { id: deviceId },
      },
    });

    if (!shift) {
      return null;
    }

    return shift;
  }

  findAll() {
    return this.shiftRepository.find();
  }

  async findOne(id: number) {
    const shift = await this.shiftRepository.findOneBy({ id });

    if (!shift) {
      throw new NotFoundException(`Shift ID ${id} not found`);
    }

    return shift;
  }

  update(id: number, updateShiftDto: UpdateShiftDto) {
    return this.shiftRepository.save({
      id: id,
      ...updateShiftDto,
    });
  }

  async remove(id: number) {
    await this.shiftRepository.delete(id);
  }

  async shiftOff(id: number) {
    const shift = await this.shiftRepository.findOneBy({ id });

    if (!shift) {
      throw new NotFoundException(`Shift ID ${id} not found`);
    }

    shift.status = ShiftStatus.CLOSED;
    shift.endShift = DateTime.now().toJSDate();

    return this.shiftRepository.save(shift);
  }

  async datatables(query: PaginateQuery): Promise<Paginated<Shift>> {
    return paginate(query, this.shiftRepository, SHIFT_PAGINATION_CONFIG);
  }
}
