import { Injectable, Logger, OnApplicationBootstrap } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Product } from '../product/entities/product.entity';
import { Branch } from '../branch/entities/branch.entity';
import { Inventory } from '../inventory/entities/inventory.entity';
import { Store } from '../store/entities/store.entity';

@Injectable()
export class StartupService implements OnApplicationBootstrap {
  private readonly logger = new Logger(StartupService.name);

  constructor(
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
    @InjectRepository(Branch)
    private branchRepository: Repository<Branch>,
    @InjectRepository(Inventory)
    private inventoryRepository: Repository<Inventory>,
    @InjectRepository(Store)
    private storeRepository: Repository<Store>,
  ) {}

  async onApplicationBootstrap() {
    this.logger.log('เริ่มต้นการตรวจสอบและสร้าง inventory สำหรับ product ที่ยังไม่มี inventory...');
    
    try {
      await this.ensureInventoryForAllProducts();
      this.logger.log('การตรวจสอบและสร้าง inventory เสร็จสิ้น');
    } catch (error) {
      this.logger.error('เกิดข้อผิดพลาดในการตรวจสอบและสร้าง inventory:', error);
    }
  }

  private async ensureInventoryForAllProducts() {
    // ดึงข้อมูล store ทั้งหมด
    const stores = await this.storeRepository.find({
      relations: ['products', 'branchs'],
    });

    for (const store of stores) {
      this.logger.log(`กำลังตรวจสอบ store: ${store.name} (ID: ${store.id})`);
      
      // ดึงข้อมูล product ทั้งหมดของ store นี้
      const products = await this.productRepository.find({
        where: { store: { id: store.id } },
        relations: ['inventories', 'inventories.branch'],
      });

      // ดึงข้อมูล branch ทั้งหมดของ store นี้
      const branches = await this.branchRepository.find({
        where: { store: { id: store.id } },
      });

      this.logger.log(`พบ ${products.length} products และ ${branches.length} branches ใน store ${store.name}`);

      let createdInventoryCount = 0;

      for (const product of products) {
        for (const branch of branches) {
          // ตรวจสอบว่า product นี้มี inventory ใน branch นี้หรือไม่
          const existingInventory = product.inventories?.find(
            inv => inv.branch?.id === branch.id
          );

          if (!existingInventory) {
            // สร้าง inventory ใหม่
            await this.createInventoryForProduct(product, branch, store);
            createdInventoryCount++;
            
            this.logger.log(
              `สร้าง inventory สำหรับ product: ${product.name} (${product.code}) ใน branch: ${branch.name} (${branch.code})`
            );
          }
        }
      }

      this.logger.log(`สร้าง inventory ใหม่ทั้งหมด ${createdInventoryCount} รายการสำหรับ store ${store.name}`);
    }
  }

  private async createInventoryForProduct(product: Product, branch: Branch, store: Store) {
    try {
      const inventory = this.inventoryRepository.create({
        product: { id: product.id },
        branch: { id: branch.id },
        store: { id: store.id },
        currentStock: 0,
        availableStock: 0,
        reservedStock: 0,
        minStock: 0,
        maxStock: null,
        averageCost: 0,
        lastCost: 0,
        lastUpdated: new Date(),
        active: true,
      });

      await this.inventoryRepository.save(inventory);
    } catch (error) {
      this.logger.error(
        `เกิดข้อผิดพลาดในการสร้าง inventory สำหรับ product ${product.code} ใน branch ${branch.code}:`,
        error
      );
      throw error;
    }
  }
}
