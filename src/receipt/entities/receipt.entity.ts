import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany } from 'typeorm';
import { Branch } from '../../branch/entities/branch.entity';
import { DecimalColumnTransformer } from '../../common/decimal-column-transformer';
import { CustomBaseEntity } from '../../common/entities';
import { Store } from '../../store/entities/store.entity';
import { User } from '../../user/entities/user.entity';
import { Vendor } from '../../vendor/entities/vendor.entity';
import { ReceiptItem } from './receipt-item.entity';

export enum ReceiptStatus {
  DRAFT = 'draft', // ร่าง
  PENDING = 'pending', // รอดำเนินการ
  APPROVED = 'approved', // อนุมัติแล้ว
  COMPLETED = 'completed', // เสร็จสิ้น
  CANCELLED = 'cancelled' // ยกเลิก
}

export enum ReceiptType {
  PURCHASE = 'purchase', // รับซื้อ
  RETURN = 'return', // รับคืน
  TRANSFER_IN = 'transfer_in', // รับโอน
  ADJUSTMENT = 'adjustment', // ปรับปรุง
  PRODUCTION = 'production', // ผลิต
  OTHER = 'other' // อื่นๆ
}

@Entity()
export class Receipt extends CustomBaseEntity {
  @Column({
    comment: 'หมายเลขใบรับของที่ไม่ซ้ำ'
  })
  @Index({ unique: true })
  receiptNo: string;

  @Column({
    type: 'enum',
    enum: ReceiptType,
    comment: 'ประเภทของใบรับของ'
  })
  type: ReceiptType;

  @Column({
    type: 'enum',
    enum: ReceiptStatus,
    default: ReceiptStatus.DRAFT,
    comment: 'สถานะของใบรับของ'
  })
  status: ReceiptStatus;

  @Column({
    type: 'date',
    comment: 'วันที่ของใบรับของ'
  })
  receiptDate: Date;

  @Column({
    nullable: true,
    comment: 'หมายเลขอ้างอิงเอกสารภายนอก (ใบสั่งซื้อ, ใบส่งของ, ฯลฯ)'
  })
  referenceNo: string;

  @ManyToOne(() => Vendor, { nullable: true })
  @JoinColumn({ name: 'vendor_id' })
  vendor: Vendor;

  @Column({
    nullable: true,
    comment: 'ข้อมูลผู้จำหน่ายหรือผู้ส่งของ (สำหรับกรณีไม่มีใน vendor master)'
  })
  supplier: string;

  @Column({
    nullable: true,
    comment: 'ที่อยู่ผู้จำหน่าย'
  })
  supplierAddress: string;

  @Column({
    nullable: true,
    comment: 'เบอร์โทรศัพท์ผู้จำหน่าย'
  })
  supplierPhone: string;

  @Column({
    type: 'numeric',
    default: 0,
    transformer: new DecimalColumnTransformer(),
    comment: 'จำนวนรายการสินค้าทั้งหมด'
  })
  totalItems: number;

  @Column({
    type: 'numeric',
    default: 0,
    transformer: new DecimalColumnTransformer(),
    comment: 'จำนวนสินค้าทั้งหมด'
  })
  totalQuantity: number;

  @Column({
    type: 'numeric',
    default: 0,
    transformer: new DecimalColumnTransformer(),
    comment: 'มูลค่ารวมก่อนภาษี'
  })
  subtotal: number;

  @Column({
    type: 'numeric',
    default: 0,
    transformer: new DecimalColumnTransformer(),
    comment: 'จำนวนภาษี'
  })
  taxAmount: number;

  @Column({
    type: 'numeric',
    default: 0,
    transformer: new DecimalColumnTransformer(),
    comment: 'ส่วนลด'
  })
  discountAmount: number;

  @Column({
    type: 'numeric',
    default: 0,
    transformer: new DecimalColumnTransformer(),
    comment: 'มูลค่ารวมสุทธิ'
  })
  totalAmount: number;

  @Column({
    nullable: true,
    comment: 'หมายเหตุเพิ่มเติมเกี่ยวกับใบรับของ'
  })
  notes: string;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'วันที่และเวลาที่อนุมัติ'
  })
  approvedAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'วันที่และเวลาที่เสร็จสิ้น'
  })
  completedAt: Date;

  // ความสัมพันธ์
  @ManyToOne(() => Branch, (branch) => branch.receipts)
  @JoinColumn({ name: 'branch_id' })
  branch: Branch;

  @ManyToOne(() => Store, (store) => store.receipts)
  @JoinColumn({ name: 'store_id' })
  store: Store;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'created_by' })
  createdBy: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'approved_by' })
  approvedBy: User;

  @OneToMany(() => ReceiptItem, (receiptItem) => receiptItem.receipt, { cascade: true })
  items: ReceiptItem[];

  constructor(partial?: Partial<Receipt>) {
    super();
    if (partial) {
      Object.assign(this, partial);
    }
  }
}
