import { Controller, Get, Post, Body, Param, Delete, HttpCode, HttpStatus, Put, Query, ParseIntPipe, UseInterceptors, ClassSerializerInterceptor, UploadedFile, BadRequestException, Header, StreamableFile, Res, Req, } from '@nestjs/common';
import { PRODUCT_PAGINATION_CONFIG, ProductService } from './product.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { ApiBody, ApiConsumes, ApiQuery, ApiTags } from '@nestjs/swagger';
import { CreateProductAttributeDto } from './dto/create-product-attribute.dto';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { FileInterceptor } from '@nestjs/platform-express';
import { Readable } from 'stream';
import { Response, Request } from 'express';
import { InventoryService } from 'src/inventory/inventory.service';
import { Branch } from 'src/branch/entities/branch.entity';

@Controller('product')
@ApiTags('สินค้า')
@Auth()
@UseInterceptors(ClassSerializerInterceptor)
export class ProductController {
  constructor(
    private readonly productService: ProductService,
    private readonly inventoryService: InventoryService,
  ) { }

  @Post('export-excel')
  async exportExcel(@Res({ passthrough: true }) res: Response) {
    const content: any = await this.productService.exportProduct();

    const file = Readable.from(content);

    res.set({
      'Content-Type':
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': `attachment; filename="export-product.xlsx"`,
      'Access-Control-Expose-Headers': 'Content-Disposition',
    });

    return new StreamableFile(file);
  }

  @Post('import-excel')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async importEmployee(@UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    return this.productService.import(file);
  }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(PRODUCT_PAGINATION_CONFIG)
  datatables(@Req() req: Request, @Paginate() query: PaginateQuery) {
    const user = req.user;

    return this.productService.datatables(query, user);
  }

  @Post('/:id/attribute')
  async createAttribute(
    @Param('id', ParseIntPipe) id: string,
    @Body() ceateProductAttributeDto: CreateProductAttributeDto,
  ) {
    return this.productService.createAttribute(+id, ceateProductAttributeDto);
  }

  @Delete('attribute/:productAttributeId')
  async deleteAttribute(
    @Param('productAttributeId', ParseIntPipe) attributeId: string,
  ) {
    await this.productService.deleteAttribute(+attributeId);
  }

  @Post()
  async create(@Req() req: Request, @Body() createProductDto: CreateProductDto) {
    const user = req.user;

    const product = await this.productService.create(createProductDto, user);

    const branches = await Branch.find({ where: { store: { id: user['storeId'] } } });

    for (const branch of branches) {
      await this.inventoryService.create({
        productId: product.id,
        branchId: branch.id,
        currentStock: 0,
        minStock: 0,
        maxStock: 0,
        reservedStock: 0,
        averageCost: 0,
        lastCost: 0,
        active: true,
      }, user);
    }

    return product;
  }

  @Get()
  @ApiQuery({ name: 'categoryId', required: false })
  @ApiQuery({ name: 'branchId', required: false })
  findAll(
    @Req() req: Request,
    @Query('categoryId') categoryId: string,
  ) {
    const user = req.user;

    return this.productService.findAll({ categoryId: +categoryId }, user);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.productService.findOne(+id);
  }

  @Post('find-by-barcode')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        barcode: { type: 'string' },
      },
    },
  })
  async findByBarcode(@Body('barcode') barcode: string) {
    return this.productService.findByBarcode(barcode);
  }

  @Put(':id')
  update(@Req() req: Request, @Param('id') id: string, @Body() updateProductDto: UpdateProductDto) {
    const user = req.user;

    return this.productService.update(+id, updateProductDto, user);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.productService.remove(+id);
  }
}
