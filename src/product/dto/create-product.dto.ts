import { ShowType } from './../entities/product.entity';
import {
  IsBoolean,
  IsNotEmpty,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateProductDto {
  @IsNotEmpty({ message: 'Code is required' })
  readonly code: string;

  @IsNotEmpty({ message: 'Name is required' })
  readonly name: string;

  @IsNotEmpty({ message: 'Price is required' })
  readonly price: number;

  readonly cost?: number;

  @IsNotEmpty({ message: 'ShowType is required' })
  readonly showType: ShowType;

  readonly image?: string;

  @ApiProperty({ example: '#00ff00' })
  readonly color?: string;

  @ApiProperty({ nullable: true, default: null })
  readonly categoryId: number | null = null;

  @ApiProperty({ nullable: true, default: null })
  readonly unitId: number | null = null;

  // readonly attributes: CreateProductAttributeDto[];

  @IsNotEmpty({ message: 'Active is required' })
  @IsBoolean()
  readonly active: boolean;

  // readonly remarkStatus: boolean;

  // readonly vatStatus: boolean;

  @ApiProperty()
  readonly barcode: string;

  readonly remark: string;

  @ApiProperty({ type: [Number], default: [] })
  readonly vendorId: number[];
}
