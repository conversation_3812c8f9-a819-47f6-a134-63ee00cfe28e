import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
import { Role } from './entities/role.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Not, Repository } from 'typeorm';
import { Permission } from '../permission/entities/permission.entity';
import { User } from '../user/entities/user.entity';
import { paginate, PaginateConfig, Paginated, PaginateQuery } from 'nestjs-paginate';

export const ROLE_PAGINATION_CONFIG: PaginateConfig<Role> = {
  sortableColumns: ['id', 'name'],
  select: ['id', 'name', 'description', 'isDeletable', 'createdAt'],
};
@Injectable()
export class RoleService {
  constructor(
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) { }

  async create(createRoleDto: CreateRoleDto, user: any) {
    const userId = user['sub'];
    const storeId = user['storeId'];

    const existingRole = await this.roleRepository.findOne({
      where: {
        name: createRoleDto.name,
        store: { id: storeId }
      }
    });

    if (existingRole) {
      throw new BadRequestException('Role name already exists in this store');
    }

    const permissions = (createRoleDto?.permissionIds?.length)
      ? await Permission.find({ where: { id: In(createRoleDto.permissionIds) } })
      : [];

    if (permissions.length !== createRoleDto?.permissionIds?.length) {
      throw new BadRequestException('The permission ID are invalid or do not exist');
    }

    const savedRole = this.roleRepository.create({
      name: createRoleDto.name,
      description: createRoleDto?.description,
      store: { id: storeId },
      isDeletable: true,
      permissions: permissions,
    });

    return this.roleRepository.save(savedRole);
  }

  findAll() {
    return this.roleRepository.find({
      select: ['id', 'name'],
      where: {
        id: Not(1),
      },
    });
  }

  async findAllMyRoles(currentuserId: number) {
    const currentUser = await this.userRepository.findOne({
      where: { id: currentuserId },
      relations: { role: true, store: true }
    });

    const allMyRoles = this.roleRepository.find({
      where: { store: { id: currentUser.store.id } },
      relations: { permissions: true }
    })

    return allMyRoles;
  }

  async findOne(id: number) {
    const role = await this.roleRepository.findOne({
      where: { id },
      relations: {
        permissions: true
      },
    });

    if (!role) {
      throw new NotFoundException('Role not found');
    }

    return role;
  }

  async update(id: number, updateRoleDto: UpdateRoleDto) {
    const role = await this.roleRepository.findOne({
      where: { id },
      relations: ['store']
    });

    if (!role) {
      throw new NotFoundException('Role not found');
    }

    const storeId = role.store.id;

    if (updateRoleDto.name) {
      const existingRole = await this.roleRepository.findOne({
        where: {
          name: updateRoleDto.name,
          store: { id: storeId },
          id: Not(id)
        }
      });

      if (existingRole) {
        throw new BadRequestException('Role name already exists in this store');
      }
    }

    let permissions = [];
    if (updateRoleDto.permissionIds?.length) {
      permissions = await Permission.find({ where: { id: In(updateRoleDto.permissionIds) } });
      if (permissions.length !== updateRoleDto.permissionIds.length) {
        throw new BadRequestException('Invalid permission IDs');
      }
    }

    Object.assign(role, {
      ...updateRoleDto,
      permissions: permissions,
    });

    await this.roleRepository.save(role);

    return this.findOne(id);
  }

  async remove(id: number) {
    const role = await this.roleRepository.findOne({ where: { id } });

    if (!role) {
      throw new NotFoundException('Role not found');
    }

    if (role.isDeletable === false) {
      throw new BadRequestException('This role cannot be deleted');
    }

    return await this.roleRepository.delete(id);
  }

  async datatables(query: PaginateQuery, user: any): Promise<Paginated<Role>> {
    return paginate(query, this.roleRepository, {
      ...ROLE_PAGINATION_CONFIG,
      where: {
        ...(user?.storeId ? { store: { id: user.storeId } } : {}),
      },
    });
  }
}
