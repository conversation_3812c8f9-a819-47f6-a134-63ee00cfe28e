import { Store } from '../../store/entities/store.entity';
import { CustomBaseEntity } from '../../common/entities/custom-base.entity';
import { Permission } from '../../permission/entities/permission.entity';
import { User } from '../../user/entities/user.entity';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  Unique,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
@Unique(['name', 'store'])
export class Role extends CustomBaseEntity {
  @Column({ comment: 'Unique name of the role' })
  // @Index({ unique: true })
  name: string; // Unique name of the role

  @Column({ nullable: true, comment: 'Description of the role' })
  description: string; // Description of the role (optional)

  @Column({ default: true })
  isDeletable: boolean;

  @OneToMany(() => User, (_) => _.role)
  users: User[];

  @ManyToMany(() => Permission, (_) => _.roles)
  @JoinTable()
  permissions: Permission[];

  @ManyToOne(() => Store, (_) => _.roles)
  store: Store;

}
