import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty } from 'class-validator';

export class CreateDeviceDto {
  // @ApiProperty()
  // @IsNotEmpty({ message: 'Code is required' })
  // code: string;

  @ApiProperty()
  @IsNotEmpty({ message: 'DeviceId is required' })
  deviceId: string;

  @IsNotEmpty({ message: 'Name is required' })
  name: string;

  @ApiProperty({ required: false })
  description?: string;

  @ApiProperty()
  active?: boolean;
}
