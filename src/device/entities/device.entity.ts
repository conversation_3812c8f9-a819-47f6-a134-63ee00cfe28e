import { Order } from '../../order/entities/order.entity';
import { CustomBaseEntity } from '../../common/entities';
import { Entity, Column, Index, ManyToOne, OneToMany } from 'typeorm';
import { Store } from '../../store/entities/store.entity';
import { Shift } from '../../shift/entities/shift.entity';

@Entity()
export class Device extends CustomBaseEntity {
  // @Column({ comment: 'Unique code for the Device' })
  // @Index()
  // code: string;

  @Column()
  @Index()
  deviceId: string;

  @Column({ comment: 'Device Name' })
  name: string;

  @Column({ nullable: true, comment: 'Description for the Device' })
  description: string;

  @Column({ default: true, comment: 'Indicates if the Device is Active' })
  active: boolean;

  @OneToMany(() => Order, (_) => _.device)
  orders: Order[];

  @ManyToOne(() => Store, (_) => _.devices)
  store: Store;

  @OneToMany(() => Shift, (_) => _.device)
  shifts: Shift[];
}
