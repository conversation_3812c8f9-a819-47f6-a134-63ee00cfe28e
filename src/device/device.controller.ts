import {
  <PERSON>,
  Get,
  Post,
  Body,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Put,
  Query,
  Req,
} from '@nestjs/common';
import { DEVICE_PAGINATION_CONFIG, DeviceService } from './device.service';
import { CreateDeviceDto } from './dto/create-device.dto';
import { UpdateDeviceDto } from './dto/update-device.dto';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { ApiQuery, ApiTags } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { Request } from 'express';

@Controller('device')
@ApiTags('อุปกรณ์')
@Auth()
export class DeviceController {
  constructor(private readonly deviceService: DeviceService) { }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(DEVICE_PAGINATION_CONFIG)
  datatables(@Req() req: Request, @Paginate() query: PaginateQuery) {
    const user = req.user;

    return this.deviceService.datatables(query, user);
  }

  @Post()
  create(@Req() req: Request, @Body() createDeviceDto: CreateDeviceDto) {
    const user = req.user;

    return this.deviceService.create(createDeviceDto, user);
  }

  @Get()
  findAll(@Req() req: Request) {
    const user = req.user;

    return this.deviceService.findAll(user);
  }

  @Get('/check-device')
  checkDevice(@Req() req: Request, @Query('deviceId') deviceId: string) {
    const user = req.user;

    return this.deviceService.checkDevice(deviceId, user);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.deviceService.findOne(+id);
  }

  @Put(':id')
  async update(
    @Req() req: Request,
    @Param('id') id: number,
    @Body() updateDeviceDto: UpdateDeviceDto,
  ) {
    const user = req.user;

    return this.deviceService.update(+id, updateDeviceDto, user);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.deviceService.remove(+id);
  }
}
