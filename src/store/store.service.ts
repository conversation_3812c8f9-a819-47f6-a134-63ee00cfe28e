import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateStoreDto } from './dto/create-store.dto';
import { UpdateStoreDto } from './dto/update-store.dto';
import { Store } from './entities/store.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import {
  PaginateConfig,
  PaginateQuery,
  Paginated,
  paginate,
} from 'nestjs-paginate';
import { User } from '../user/entities/user.entity';
import { Role } from '../role/entities/role.entity';
import { Permission } from 'src/permission/entities/permission.entity';

export const STORE_PAGINATION_CONFIG: PaginateConfig<Store> = {
  sortableColumns: ['id', 'name'],
  select: ['id', 'code', 'name', 'createdAt'],
};
@Injectable()
export class StoreService {
  constructor(
    @InjectRepository(Store)
    private storeRepository: Repository<Store>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
  ) { }

  create(createStoreDto: CreateStoreDto) {
    const store = this.storeRepository.create(createStoreDto);

    return this.storeRepository.save(store);
  }

  async userCreateStore(createStoreDto: CreateStoreDto, currentuserId: number) {

    const currentUser = await this.userRepository.findOne({
      where: { id: currentuserId },
      relations: { role: true, store: true }
    });

    const createStore = this.storeRepository.create(createStoreDto);
    const savedStore = await this.storeRepository.save(createStore);

    const allPermissions = await Permission.find();

    const roleNames = ['Owner', 'Admin', 'Supervisor', 'Cashier'];

    const rolesToSave = roleNames.map((roleName) => {
      const isOwner = roleName === 'Owner';

      return this.roleRepository.create({
        name: roleName,
        store: savedStore,
        isDeletable: !isOwner,
        permissions: isOwner ? allPermissions : [], // ถ้าเป็น Owner ให้มีทุก permission
      });
    });

    const savedRoles = await this.roleRepository.save(rolesToSave);

    const ownerRole = savedRoles.find((role) => role.name === 'Owner');

    currentUser.store = savedStore;
    currentUser.role = ownerRole;
    await this.userRepository.save(currentUser);

    return savedStore;
  }

  findAll() {
    return this.storeRepository.find();
  }

  async findOne(id: number) {
    const store = await this.storeRepository.findOne({ where: { id } });

    if (!store) throw new NotFoundException('store not found');

    return store;
  }

  async update(id: number, updateStoreDto: UpdateStoreDto) {
    const store = await this.findOneById(id);
    if (!store) throw new NotFoundException('store not found');

    return this.storeRepository.update(id, updateStoreDto);
  }

  async remove(id: number) {
    const store = await this.findOneById(id);
    if (!store) throw new NotFoundException('store not found');

    await this.storeRepository.softDelete(id);
  }

  findOneById(id: number) {
    return this.storeRepository.findOneBy({ id });
  }

  async datatables(query: PaginateQuery): Promise<Paginated<Store>> {
    return paginate(query, this.storeRepository, STORE_PAGINATION_CONFIG);
  }
}
