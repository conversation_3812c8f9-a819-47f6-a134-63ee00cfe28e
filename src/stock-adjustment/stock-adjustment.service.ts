import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, QueryRunner, Like } from 'typeorm';
import { StockAdjustment, StockAdjustmentStatus } from './entities/stock-adjustment.entity';
import { StockAdjustmentItem, AdjustmentItemType } from './entities/stock-adjustment-item.entity';
import { Product } from '../product/entities/product.entity';
import { Branch } from '../branch/entities/branch.entity';
import { Inventory } from '../inventory/entities/inventory.entity';
import { InventoryTransaction, InventoryTransactionType } from '../inventory/entities/inventory-transaction.entity';
import { CreateStockAdjustmentDto } from './dto/create-stock-adjustment.dto';
import { UpdateStockAdjustmentDto, ApproveStockAdjustmentDto, CompleteStockAdjustmentDto } from './dto/update-stock-adjustment.dto';
import { PaginateQuery, Paginated, paginate, PaginateConfig } from 'nestjs-paginate';

export const STOCK_ADJUSTMENT_PAGINATION_CONFIG: PaginateConfig<StockAdjustment> = {
  sortableColumns: ['id', 'adjustmentNo', 'adjustmentDate', 'netAdjustmentValue', 'status', 'createdAt'],
  defaultSortBy: [['createdAt', 'DESC']],
  searchableColumns: ['adjustmentNo', 'referenceNo', 'reason', 'notes'],
  select: [
    'id', 'adjustmentNo', 'type', 'status', 'adjustmentDate', 'referenceNo', 'reason',
    'totalItems', 'totalIncreaseQuantity', 'totalDecreaseQuantity', 'netAdjustmentValue',
    'notes', 'createdAt', 'updatedAt',
    'branch.id', 'branch.name', 'createdBy.id', 'createdBy.firstName', 'createdBy.lastName'
  ],
  relations: ['branch', 'createdBy'],
  filterableColumns: {
    status: true,
    type: true,
    'branch.id': true,
  }
};

@Injectable()
export class StockAdjustmentService {
  constructor(
    @InjectRepository(StockAdjustment)
    private stockAdjustmentRepository: Repository<StockAdjustment>,
    @InjectRepository(StockAdjustmentItem)
    private stockAdjustmentItemRepository: Repository<StockAdjustmentItem>,
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
    @InjectRepository(Branch)
    private branchRepository: Repository<Branch>,
    @InjectRepository(Inventory)
    private inventoryRepository: Repository<Inventory>,
    @InjectRepository(InventoryTransaction)
    private transactionRepository: Repository<InventoryTransaction>,
    private dataSource: DataSource,
  ) {}

  async datatables(query: PaginateQuery, user: any): Promise<Paginated<StockAdjustment>> {
    return paginate(query, this.stockAdjustmentRepository, {
      ...STOCK_ADJUSTMENT_PAGINATION_CONFIG,
      where: {
        ...(user?.storeId ? { store: { id: user.storeId } } : {}),
      },
    });
  }

  async create(createStockAdjustmentDto: CreateStockAdjustmentDto, user: any): Promise<StockAdjustment> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // ตรวจสอบสาขา
      const branch = await this.branchRepository.findOne({
        where: {
          id: createStockAdjustmentDto.branchId,
          ...(user?.storeId ? { store: { id: user.storeId } } : {}),
        },
      });

      if (!branch) {
        throw new NotFoundException('ไม่พบสาขา');
      }

      // สร้างหมายเลขใบปรับสต็อก
      const adjustmentNo = await this.generateAdjustmentNo(createStockAdjustmentDto.type);

      // คำนวณยอดรวม
      let totalItems = 0;
      let totalIncreaseQuantity = 0;
      let totalDecreaseQuantity = 0;
      let totalIncreaseValue = 0;
      let totalDecreaseValue = 0;

      for (const item of createStockAdjustmentDto.items) {
        // ตรวจสอบสินค้าและ inventory
        const inventory = await this.inventoryRepository.findOne({
          where: {
            id: item.inventoryId,
            product: { id: item.productId },
            ...(user?.storeId ? { store: { id: user.storeId } } : {}),
          },
          relations: ['product'],
        });

        if (!inventory) {
          throw new NotFoundException(`ไม่พบข้อมูลสินค้าคงคลัง ID: ${item.inventoryId}`);
        }

        totalItems++;

        // คำนวณจำนวนที่ปรับ
        let adjustmentQuantity = 0;
        const currentStock = inventory.currentStock;

        switch (item.adjustmentType) {
          case AdjustmentItemType.INCREASE:
            adjustmentQuantity = item.quantity;
            totalIncreaseQuantity += adjustmentQuantity;
            break;
          case AdjustmentItemType.DECREASE:
            adjustmentQuantity = -item.quantity;
            totalDecreaseQuantity += item.quantity;
            break;
          case AdjustmentItemType.SET:
            adjustmentQuantity = item.quantity - currentStock;
            if (adjustmentQuantity > 0) {
              totalIncreaseQuantity += adjustmentQuantity;
            } else {
              totalDecreaseQuantity += Math.abs(adjustmentQuantity);
            }
            break;
        }

        // คำนวณมูลค่า
        const unitCost = item.unitCost || inventory.averageCost || 0;
        const adjustmentValue = adjustmentQuantity * unitCost;

        if (adjustmentValue > 0) {
          totalIncreaseValue += adjustmentValue;
        } else {
          totalDecreaseValue += Math.abs(adjustmentValue);
        }
      }

      const netAdjustmentValue = totalIncreaseValue - totalDecreaseValue;

      // สร้างใบปรับสต็อก
      const stockAdjustment = this.stockAdjustmentRepository.create({
        adjustmentNo,
        type: createStockAdjustmentDto.type,
        status: createStockAdjustmentDto.status,
        adjustmentDate: new Date(createStockAdjustmentDto.adjustmentDate),
        referenceNo: createStockAdjustmentDto.referenceNo,
        reason: createStockAdjustmentDto.reason,
        totalItems,
        totalIncreaseQuantity,
        totalDecreaseQuantity,
        totalIncreaseValue,
        totalDecreaseValue,
        netAdjustmentValue,
        notes: createStockAdjustmentDto.notes,
        branch: { id: createStockAdjustmentDto.branchId },
        store: { id: user?.storeId },
        createdBy: { id: user?.id },
      });

      const savedStockAdjustment = await queryRunner.manager.save(stockAdjustment);

      // สร้างรายการปรับสต็อก
      for (const itemDto of createStockAdjustmentDto.items) {
        const inventory = await this.inventoryRepository.findOne({
          where: { id: itemDto.inventoryId },
          relations: ['product'],
        });

        const currentStock = inventory.currentStock;
        let stockAfter = currentStock;
        let adjustmentQuantity = 0;

        switch (itemDto.adjustmentType) {
          case AdjustmentItemType.INCREASE:
            adjustmentQuantity = itemDto.quantity;
            stockAfter = currentStock + adjustmentQuantity;
            break;
          case AdjustmentItemType.DECREASE:
            adjustmentQuantity = -itemDto.quantity;
            stockAfter = currentStock + adjustmentQuantity;
            break;
          case AdjustmentItemType.SET:
            stockAfter = itemDto.quantity;
            adjustmentQuantity = stockAfter - currentStock;
            break;
        }

        const unitCost = itemDto.unitCost || inventory.averageCost || 0;
        const adjustmentValue = adjustmentQuantity * unitCost;

        const stockAdjustmentItem = this.stockAdjustmentItemRepository.create({
          adjustmentType: itemDto.adjustmentType,
          stockBefore: currentStock,
          stockAfter,
          adjustmentQuantity,
          unitCost,
          adjustmentValue,
          reason: itemDto.reason,
          notes: itemDto.notes,
          batchNo: itemDto.batchNo,
          expiryDate: itemDto.expiryDate ? new Date(itemDto.expiryDate) : null,
          location: itemDto.location,
          stockAdjustment: { id: savedStockAdjustment.id },
          product: { id: itemDto.productId },
          inventory: { id: itemDto.inventoryId },
        });

        await queryRunner.manager.save(stockAdjustmentItem);
      }

      await queryRunner.commitTransaction();
      return this.findOne(savedStockAdjustment.id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async findAll(user: any, filters?: any): Promise<StockAdjustment[]> {
    const where: any = {
      ...(user?.storeId ? { store: { id: user.storeId } } : {}),
    };

    if (filters?.status) {
      where.status = filters.status;
    }

    if (filters?.type) {
      where.type = filters.type;
    }

    if (filters?.branchId) {
      where.branch = { id: filters.branchId };
    }

    return this.stockAdjustmentRepository.find({
      where,
      relations: ['branch', 'store', 'createdBy', 'items', 'items.product', 'items.inventory'],
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: number): Promise<StockAdjustment> {
    const stockAdjustment = await this.stockAdjustmentRepository.findOne({
      where: { id },
      relations: [
        'branch', 'store', 'createdBy', 'approvedBy',
        'items', 'items.product', 'items.inventory'
      ],
    });

    if (!stockAdjustment) {
      throw new NotFoundException('ไม่พบใบปรับสต็อก');
    }

    return stockAdjustment;
  }

  async update(id: number, updateStockAdjustmentDto: UpdateStockAdjustmentDto, user: any): Promise<StockAdjustment> {
    const stockAdjustment = await this.findOne(id);

    // ตรวจสอบสิทธิ์การแก้ไข
    if (stockAdjustment.status === StockAdjustmentStatus.COMPLETED) {
      throw new BadRequestException('ไม่สามารถแก้ไขใบปรับสต็อกที่เสร็จสิ้นแล้ว');
    }

    if (user?.storeId && stockAdjustment.store.id !== user.storeId) {
      throw new BadRequestException('ไม่มีสิทธิ์แก้ไขใบปรับสต็อกนี้');
    }

    // อัปเดตข้อมูล
    Object.assign(stockAdjustment, updateStockAdjustmentDto);

    if (updateStockAdjustmentDto.adjustmentDate) {
      stockAdjustment.adjustmentDate = new Date(updateStockAdjustmentDto.adjustmentDate);
    }

    await this.stockAdjustmentRepository.save(stockAdjustment);
    return this.findOne(id);
  }

  async remove(id: number, user: any): Promise<void> {
    const stockAdjustment = await this.findOne(id);

    // ตรวจสอบสิทธิ์การลบ
    if (stockAdjustment.status === StockAdjustmentStatus.COMPLETED) {
      throw new BadRequestException('ไม่สามารถลบใบปรับสต็อกที่เสร็จสิ้นแล้ว');
    }

    if (user?.storeId && stockAdjustment.store.id !== user.storeId) {
      throw new BadRequestException('ไม่มีสิทธิ์ลบใบปรับสต็อกนี้');
    }

    await this.stockAdjustmentRepository.softDelete(id);
  }

  async approve(id: number, approveDto: ApproveStockAdjustmentDto, user: any): Promise<StockAdjustment> {
    const stockAdjustment = await this.findOne(id);

    if (stockAdjustment.status !== StockAdjustmentStatus.PENDING) {
      throw new BadRequestException('สามารถอนุมัติได้เฉพาะใบปรับสต็อกที่มีสถานะรอดำเนินการเท่านั้น');
    }

    stockAdjustment.status = StockAdjustmentStatus.APPROVED;
    stockAdjustment.approvedAt = new Date();
    stockAdjustment.approvedBy = { id: user?.id } as any;

    if (approveDto.approvalNotes) {
      stockAdjustment.notes = stockAdjustment.notes ? 
        `${stockAdjustment.notes}\n\nการอนุมัติ: ${approveDto.approvalNotes}` : 
        `การอนุมัติ: ${approveDto.approvalNotes}`;
    }

    await this.stockAdjustmentRepository.save(stockAdjustment);
    return this.findOne(id);
  }

  async complete(id: number, completeDto: CompleteStockAdjustmentDto, user: any): Promise<StockAdjustment> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const stockAdjustment = await this.findOne(id);

      if (stockAdjustment.status !== StockAdjustmentStatus.APPROVED) {
        throw new BadRequestException('สามารถเสร็จสิ้นได้เฉพาะใบปรับสต็อกที่อนุมัติแล้วเท่านั้น');
      }

      // อัปเดตสต็อกสินค้า
      for (const item of stockAdjustment.items) {
        await this.updateInventoryFromAdjustment(item, queryRunner);
      }

      // อัปเดตสถานะใบปรับสต็อก
      stockAdjustment.status = StockAdjustmentStatus.COMPLETED;
      stockAdjustment.completedAt = new Date();

      if (completeDto.completionNotes) {
        stockAdjustment.notes = stockAdjustment.notes ? 
          `${stockAdjustment.notes}\n\nการเสร็จสิ้น: ${completeDto.completionNotes}` : 
          `การเสร็จสิ้น: ${completeDto.completionNotes}`;
      }

      await queryRunner.manager.save(stockAdjustment);
      await queryRunner.commitTransaction();

      return this.findOne(id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  private async updateInventoryFromAdjustment(adjustmentItem: StockAdjustmentItem, queryRunner: QueryRunner): Promise<void> {
    // อัปเดต inventory
    const inventory = await queryRunner.manager.findOne(Inventory, {
      where: { id: adjustmentItem.inventory.id },
      relations: ['product', 'branch', 'store'],
    });

    if (!inventory) {
      throw new NotFoundException('ไม่พบข้อมูลสินค้าคงคลัง');
    }

    const stockBefore = inventory.currentStock;
    inventory.currentStock = adjustmentItem.stockAfter;
    inventory.availableStock = adjustmentItem.stockAfter - inventory.reservedStock;
    inventory.lastUpdated = new Date();

    await queryRunner.manager.save(inventory);

    // สร้าง inventory transaction
    const transactionType = InventoryTransactionType.ADJUSTMENT;

    const transactionNo = `ADJ-${adjustmentItem.stockAdjustment.adjustmentNo}-${adjustmentItem.product.id}`;
    const transaction = queryRunner.manager.create(InventoryTransaction, {
      transactionNo,
      type: transactionType,
      quantity: Math.abs(adjustmentItem.adjustmentQuantity),
      unitCost: adjustmentItem.unitCost,
      totalCost: Math.abs(adjustmentItem.adjustmentValue),
      stockBefore,
      stockAfter: adjustmentItem.stockAfter,
      referenceNo: adjustmentItem.stockAdjustment.adjustmentNo,
      notes: `ปรับสต็อก: ${adjustmentItem.reason || adjustmentItem.stockAdjustment.reason || 'ไม่ระบุเหตุผล'}`,
      batchNo: adjustmentItem.batchNo,
      expiryDate: adjustmentItem.expiryDate,
      inventory,
      createdBy: adjustmentItem.stockAdjustment.createdBy,
    });

    await queryRunner.manager.save(transaction);
  }

  private async generateAdjustmentNo(type: string): Promise<string> {
    const prefix = 'ADJ';
    const year = new Date().getFullYear().toString().slice(-2);
    const month = (new Date().getMonth() + 1).toString().padStart(2, '0');
    
    const lastAdjustment = await this.stockAdjustmentRepository.findOne({
      where: { adjustmentNo: Like(`${prefix}-${year}${month}%`) },
      order: { adjustmentNo: 'DESC' },
    });

    let sequence = 1;
    if (lastAdjustment) {
      const lastSequence = parseInt(lastAdjustment.adjustmentNo.split('-')[1].slice(4));
      sequence = lastSequence + 1;
    }

    return `${prefix}-${year}${month}${sequence.toString().padStart(4, '0')}`;
  }
}
