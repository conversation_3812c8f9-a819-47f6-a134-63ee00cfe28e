import { CustomBaseEntity } from "../../common/entities";
import { Column, Entity } from "typeorm";

@Entity()
export class WorkShift extends CustomBaseEntity {
  @Column({ comment: 'Shift Code' })
  code: string;

  @Column({ comment: 'Shift Name' })
  name: string;

  @Column('time', { comment: 'Start Time' })
  startTime: Date;

  @Column('time', { comment: 'End Time' })
  endTime: Date;

  @Column({ comment: 'Description', nullable: true })
  description: string;

  @Column({ comment: 'Is Active' })
  isActive: boolean;
}
