import { Injectable } from '@nestjs/common';
import { CreateWorkShiftDto } from './dto/create-work-shift.dto';
import { UpdateWorkShiftDto } from './dto/update-work-shift.dto';

@Injectable()
export class WorkShiftService {
  create(createWorkShiftDto: CreateWorkShiftDto) {
    return 'This action adds a new workShift';
  }

  findAll() {
    return `This action returns all workShift`;
  }

  findOne(id: number) {
    return `This action returns a #${id} workShift`;
  }

  update(id: number, updateWorkShiftDto: UpdateWorkShiftDto) {
    return `This action updates a #${id} workShift`;
  }

  remove(id: number) {
    return `This action removes a #${id} workShift`;
  }
}
