import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateBranchDto } from './dto/create-branch.dto';
import { UpdateBranchDto } from './dto/update-branch.dto';
import { Branch } from './entities/branch.entity';
import { Not, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import {
  PaginateConfig,
  PaginateQuery,
  Paginated,
  paginate,
} from 'nestjs-paginate';
import { Device } from 'src/device/entities/device.entity';

export const BRANCH_PAGINATION_CONFIG: PaginateConfig<Branch> = {
  sortableColumns: ['id', 'name'],
  select: ['id', 'code', 'name', 'createdAt'],
};
@Injectable()
export class BranchService {
  constructor(
    @InjectRepository(Branch)
    private branchRepository: Repository<Branch>,
  ) { }

  async datatables(query: PaginateQuery, user: any): Promise<Paginated<Branch>> {
    return paginate(
      query,
      this.branchRepository,
      {
        ...BRANCH_PAGINATION_CONFIG,
        where: {
          ...(user?.storeId ? { store: { id: user.storeId } } : {}),
        },
      },
    );
  }

  async create(createBranchDto: CreateBranchDto, user: any) {
    const storeId = user['storeId'];

    const { active, description, ...data } = createBranchDto;

    //check branch code exist
    const branchCode = await Branch.existsBy({
      code: createBranchDto?.code,
      store: { id: storeId },
    });
    if (branchCode) {
      throw new BadRequestException('Branch code already.');
    }

    if (
      createBranchDto.active === null ||
      createBranchDto.active === undefined
    ) {
      throw new NotFoundException('Active status is required');
    }

    const branch = this.branchRepository.create({
      ...data,
      description: description || null,
      active: active || false,
      store: { id: storeId },
    });

    return this.branchRepository.save(branch);
  }

  findAll(user: any) {
    if (user?.storeId) {
      return this.branchRepository.find({
        where: {
          store: { id: user.storeId },
        },
      });
    }

    return this.branchRepository.find();
  }

  async findOne(id: number) {
    const branch = await this.branchRepository.findOne({
      where: { id }
    });

    if (!branch) throw new NotFoundException('branch not found');

    return branch;
  }

  async update(id: number, updateBranchDto: UpdateBranchDto) {
    const branch = await this.findOneById(id);
    if (!branch) {
      throw new NotFoundException('branch not found');
    }

    //check branch code exist
    const branchCode = await Branch.existsBy({
      code: updateBranchDto?.code,
      id: Not(id),
    });
    if (branchCode) {
      throw new BadRequestException('Branch code already.');
    }

    const updateBranch = Branch.create({
      ...updateBranchDto,
      description: updateBranchDto?.description || null,
      active: updateBranchDto?.active || false,
    });

    return this.branchRepository.update(id, {
      ...updateBranch,
    });
  }

  async remove(id: number) {
    const branch = await this.findOneById(id);
    if (!branch) throw new NotFoundException('branch not found');
    if (branch.id == 1)
      throw new NotFoundException('Cannot Delete this branch');
    branch.code = `${Date.now()}-${branch.code}`;
    await branch.save();

    await this.branchRepository.softDelete(id);
  }

  findOneById(id: number) {
    return this.branchRepository.findOne({
      where: { id },
    });
  }
}
