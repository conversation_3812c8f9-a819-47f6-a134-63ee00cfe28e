import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, QueryRunner } from 'typeorm';
import {
  PaginateConfig,
  PaginateQuery,
  Paginated,
  paginate,
} from 'nestjs-paginate';
import { DateTime } from 'luxon';

import { Inventory } from './entities/inventory.entity';
import { InventoryTransaction, InventoryTransactionType } from './entities/inventory-transaction.entity';
import { CreateInventoryDto } from './dto/create-inventory.dto';
import { UpdateInventoryDto } from './dto/update-inventory.dto';
import { CreateInventoryTransactionDto } from './dto/create-inventory-transaction.dto';
import { StockAdjustmentDto, StockTransferDto } from './dto/stock-adjustment.dto';
import { Product } from '../product/entities/product.entity';
import { Branch } from '../branch/entities/branch.entity';

export const INVENTORY_PAGINATION_CONFIG: PaginateConfig<Inventory> = {
  sortableColumns: ['id', 'currentStock', 'minStock', 'lastUpdated'],
  searchableColumns: ['product.name', 'product.code', 'branch.name', 'location'],
  defaultSortBy: [['lastUpdated', 'DESC']],
  relations: ['product', 'branch', 'store'],
};

export const INVENTORY_TRANSACTION_PAGINATION_CONFIG: PaginateConfig<InventoryTransaction> = {
  sortableColumns: ['id', 'transactionDate', 'quantity', 'type'],
  searchableColumns: ['transactionNo', 'referenceNo', 'supplier', 'notes'],
  defaultSortBy: [['transactionDate', 'DESC']],
  relations: ['inventory', 'inventory.product', 'inventory.branch', 'createdBy'],
};

@Injectable()
export class InventoryService {
  constructor(
    @InjectRepository(Inventory)
    private inventoryRepository: Repository<Inventory>,
    @InjectRepository(InventoryTransaction)
    private transactionRepository: Repository<InventoryTransaction>,
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
    @InjectRepository(Branch)
    private branchRepository: Repository<Branch>,
    private dataSource: DataSource,
  ) {}

  async datatables(query: PaginateQuery, user: any): Promise<Paginated<Inventory>> {
    return paginate(query, this.inventoryRepository, {
      ...INVENTORY_PAGINATION_CONFIG,
      where: {
        ...(user?.storeId ? { store: { id: user.storeId } } : {}),
      },
    });
  }

  async transactionDatatables(query: PaginateQuery, user: any): Promise<Paginated<InventoryTransaction>> {
    return paginate(query, this.transactionRepository, {
      ...INVENTORY_TRANSACTION_PAGINATION_CONFIG,
      where: {
        ...(user?.storeId ? { inventory: { store: { id: user.storeId } } } : {}),
      },
    });
  }

  async create(createInventoryDto: CreateInventoryDto, user: any): Promise<Inventory> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // ตรวจสอบว่ามีสินค้าคงคลังสำหรับสินค้า-สาขานี้อยู่แล้วหรือไม่
      const existingInventory = await this.inventoryRepository.findOne({
        where: {
          product: { id: createInventoryDto.productId },
          branch: { id: createInventoryDto.branchId },
          ...(user?.storeId ? { store: { id: user.storeId } } : {}),
        },
      });

      if (existingInventory) {
        throw new ConflictException('มีสินค้าคงคลังสำหรับสินค้า-สาขานี้อยู่แล้ว');
      }

      // ตรวจสอบว่าสินค้าและสาขามีอยู่และเป็นของร้านค้าของผู้ใช้
      const product = await this.productRepository.findOne({
        where: {
          id: createInventoryDto.productId,
          ...(user?.storeId ? { store: { id: user.storeId } } : {}),
        },
      });

      if (!product) {
        throw new NotFoundException('ไม่พบสินค้า');
      }

      const branch = await this.branchRepository.findOne({
        where: {
          id: createInventoryDto.branchId,
          ...(user?.storeId ? { store: { id: user.storeId } } : {}),
        },
      });

      if (!branch) {
        throw new NotFoundException('ไม่พบสาขา');
      }

      // คำนวณสต็อกที่พร้อมใช้
      const availableStock = (createInventoryDto.currentStock || 0) - (createInventoryDto.reservedStock || 0);

      // สร้างข้อมูลสินค้าคงคลัง
      const inventory = this.inventoryRepository.create({
        ...createInventoryDto,
        availableStock,
        lastUpdated: new Date(),
        product: { id: createInventoryDto.productId },
        branch: { id: createInventoryDto.branchId },
        store: { id: user?.storeId },
      });

      const savedInventory = await queryRunner.manager.save(inventory);

      // สร้างธุรกรรมเริ่มต้นหากมีสต็อกเริ่มต้น
      if (createInventoryDto.currentStock && createInventoryDto.currentStock > 0) {
        await this.createTransaction(
          {
            inventoryId: savedInventory.id,
            type: InventoryTransactionType.INITIAL,
            quantity: createInventoryDto.currentStock,
            unitCost: createInventoryDto.averageCost,
            notes: 'บันทึกสต็อกเริ่มต้น',
          },
          user,
          queryRunner,
        );
      }

      await queryRunner.commitTransaction();
      return this.findOne(savedInventory.id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async findAll(user: any, filters?: any): Promise<Inventory[]> {
    const where: any = {
      ...(user?.storeId ? { store: { id: user.storeId } } : {}),
    };

    if (filters?.productId) {
      where.product = { id: filters.productId };
    }

    if (filters?.branchId) {
      where.branch = { id: filters.branchId };
    }

    if (filters?.lowStock) {
      // จะจัดการในตัวสร้างคิวรีสำหรับเงื่อนไขที่ซับซ้อน
    }

    return this.inventoryRepository.find({
      where,
      relations: ['product', 'branch', 'store'],
      order: { lastUpdated: 'DESC' },
    });
  }

  async findOne(id: number): Promise<Inventory> {
    const inventory = await this.inventoryRepository.findOne({
      where: { id },
      relations: ['product', 'branch', 'store', 'transactions'],
    });

    if (!inventory) {
      throw new NotFoundException('ไม่พบสินค้าคงคลัง');
    }

    return inventory;
  }

  async findByProductAndBranch(productId: number, branchId: number, user: any): Promise<Inventory> {
    const inventory = await this.inventoryRepository.findOne({
      where: {
        product: { id: productId },
        branch: { id: branchId },
        ...(user?.storeId ? { store: { id: user.storeId } } : {}),
      },
      relations: ['product', 'branch', 'store'],
    });

    if (!inventory) {
      throw new NotFoundException('ไม่พบสินค้าคงคลังสำหรับสินค้า-สาขานี้');
    }

    return inventory;
  }

  async update(id: number, updateInventoryDto: UpdateInventoryDto): Promise<Inventory> {
    const inventory = await this.findOne(id);

    // คำนวณสต็อกที่พร้อมใช้ใหม่หากมีการอัปเดตสต็อกปัจจุบันหรือสต็อกที่จองไว้
    let availableStock = inventory.availableStock;
    if (updateInventoryDto.currentStock !== undefined || updateInventoryDto.reservedStock !== undefined) {
      const newCurrentStock = updateInventoryDto.currentStock ?? inventory.currentStock;
      const newReservedStock = updateInventoryDto.reservedStock ?? inventory.reservedStock;
      availableStock = newCurrentStock - newReservedStock;
    }

    await this.inventoryRepository.update(id, {
      ...updateInventoryDto,
      availableStock,
      lastUpdated: new Date(),
    });

    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    const inventory = await this.findOne(id);

    const nowStr = DateTime.now().toLocal().toString();
    inventory.location = `${nowStr}-${inventory.location || ''}`;
    await inventory.save();

    await this.inventoryRepository.softRemove(inventory);
  }

  async adjustStock(stockAdjustmentDto: StockAdjustmentDto, user: any): Promise<Inventory> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const inventory = await this.findOne(stockAdjustmentDto.inventoryId);
      const oldQuantity = inventory.currentStock;
      const newQuantity = stockAdjustmentDto.newQuantity;
      const difference = newQuantity - oldQuantity;

      if (difference === 0) {
        throw new BadRequestException('ไม่จำเป็นต้องปรับปรุง - จำนวนเท่าเดิม');
      }

      // อัปเดตสินค้าคงคลัง
      inventory.currentStock = newQuantity;
      inventory.availableStock = newQuantity - inventory.reservedStock;
      inventory.lastUpdated = new Date();
      await queryRunner.manager.save(inventory);

      // สร้างธุรกรรมการปรับปรุง
      await this.createTransaction(
        {
          inventoryId: inventory.id,
          type: InventoryTransactionType.ADJUSTMENT,
          quantity: Math.abs(difference),
          referenceNo: stockAdjustmentDto.referenceNo,
          notes: stockAdjustmentDto.notes,
          reason: stockAdjustmentDto.reason || (difference > 0 ? 'Stock increase' : 'Stock decrease'),
        },
        user,
        queryRunner,
      );

      await queryRunner.commitTransaction();
      return this.findOne(inventory.id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async transferStock(stockTransferDto: StockTransferDto, user: any): Promise<{ from: Inventory; to: Inventory }> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const fromInventory = await this.findOne(stockTransferDto.fromInventoryId);
      const toInventory = await this.findOne(stockTransferDto.toInventoryId);

      if (fromInventory.availableStock < stockTransferDto.quantity) {
        throw new BadRequestException('สต็อกไม่เพียงพอสำหรับการโอน');
      }

      // อัปเดตสินค้าคงคลังต้นทาง
      fromInventory.currentStock -= stockTransferDto.quantity;
      fromInventory.availableStock -= stockTransferDto.quantity;
      fromInventory.lastUpdated = new Date();
      await queryRunner.manager.save(fromInventory);

      // อัปเดตสินค้าคงคลังปลายทาง
      toInventory.currentStock += stockTransferDto.quantity;
      toInventory.availableStock += stockTransferDto.quantity;
      toInventory.lastUpdated = new Date();
      await queryRunner.manager.save(toInventory);

      // สร้างธุรกรรมการโอนออก
      await this.createTransaction(
        {
          inventoryId: fromInventory.id,
          type: InventoryTransactionType.TRANSFER,
          quantity: -stockTransferDto.quantity,
          referenceNo: stockTransferDto.referenceNo,
          notes: `Transfer to ${toInventory.branch.name} - ${stockTransferDto.notes || ''}`,
        },
        user,
        queryRunner,
      );

      // สร้างธุรกรรมการโอนเข้า
      await this.createTransaction(
        {
          inventoryId: toInventory.id,
          type: InventoryTransactionType.TRANSFER,
          quantity: stockTransferDto.quantity,
          referenceNo: stockTransferDto.referenceNo,
          notes: `Transfer from ${fromInventory.branch.name} - ${stockTransferDto.notes || ''}`,
        },
        user,
        queryRunner,
      );

      await queryRunner.commitTransaction();
      return {
        from: await this.findOne(fromInventory.id),
        to: await this.findOne(toInventory.id),
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async stockIn(createTransactionDto: CreateInventoryTransactionDto, user: any): Promise<Inventory> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const inventory = await this.findOne(createTransactionDto.inventoryId);

      // อัปเดตสินค้าคงคลัง
      inventory.currentStock += createTransactionDto.quantity;
      inventory.availableStock += createTransactionDto.quantity;

      // อัปเดตต้นทุนหากมีการระบุ
      if (createTransactionDto.unitCost) {
        inventory.lastCost = createTransactionDto.unitCost;

        // คำนวณต้นทุนเฉลี่ยใหม่โดยใช้ค่าเฉลี่ยถ่วงน้ำหนัก
        if (inventory.averageCost && inventory.currentStock > createTransactionDto.quantity) {
          const oldValue = (inventory.currentStock - createTransactionDto.quantity) * inventory.averageCost;
          const newValue = createTransactionDto.quantity * createTransactionDto.unitCost;
          inventory.averageCost = (oldValue + newValue) / inventory.currentStock;
        } else {
          inventory.averageCost = createTransactionDto.unitCost;
        }
      }

      inventory.lastUpdated = new Date();
      await queryRunner.manager.save(inventory);

      // สร้างธุรกรรม
      await this.createTransaction(
        {
          ...createTransactionDto,
          type: InventoryTransactionType.STOCK_IN,
        },
        user,
        queryRunner,
      );

      await queryRunner.commitTransaction();
      return this.findOne(inventory.id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async stockOut(createTransactionDto: CreateInventoryTransactionDto, user: any): Promise<Inventory> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const inventory = await this.findOne(createTransactionDto.inventoryId);

      if (inventory.availableStock < createTransactionDto.quantity) {
        throw new BadRequestException('สต็อกที่พร้อมใช้ไม่เพียงพอ');
      }

      // อัปเดตสินค้าคงคลัง
      inventory.currentStock -= createTransactionDto.quantity;
      inventory.availableStock -= createTransactionDto.quantity;
      inventory.lastUpdated = new Date();
      await queryRunner.manager.save(inventory);

      // สร้างธุรกรรม
      await this.createTransaction(
        {
          ...createTransactionDto,
          type: InventoryTransactionType.STOCK_OUT,
        },
        user,
        queryRunner,
      );

      await queryRunner.commitTransaction();
      return this.findOne(inventory.id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async getLowStockItems(user: any): Promise<Inventory[]> {
    return this.inventoryRepository
      .createQueryBuilder('inventory')
      .leftJoinAndSelect('inventory.product', 'product')
      .leftJoinAndSelect('inventory.branch', 'branch')
      .leftJoinAndSelect('inventory.store', 'store')
      .where('inventory.currentStock <= inventory.minStock')
      .andWhere('inventory.active = :active', { active: true })
      .andWhere(user?.storeId ? 'inventory.store.id = :storeId' : '1=1', { storeId: user?.storeId })
      .orderBy('inventory.currentStock', 'ASC')
      .getMany();
  }

  async getInventoryReport(user: any, filters?: any): Promise<any> {
    const queryBuilder = this.inventoryRepository
      .createQueryBuilder('inventory')
      .leftJoinAndSelect('inventory.product', 'product')
      .leftJoinAndSelect('inventory.branch', 'branch')
      .leftJoinAndSelect('inventory.store', 'store')
      .where(user?.storeId ? 'inventory.store.id = :storeId' : '1=1', { storeId: user?.storeId });

    if (filters?.branchId) {
      queryBuilder.andWhere('inventory.branch.id = :branchId', { branchId: filters.branchId });
    }

    if (filters?.categoryId) {
      queryBuilder.andWhere('product.category.id = :categoryId', { categoryId: filters.categoryId });
    }

    const inventories = await queryBuilder.getMany();

    const summary = {
      totalItems: inventories.length,
      totalStockValue: inventories.reduce((sum, inv) => sum + inv.stockValue, 0),
      lowStockItems: inventories.filter(inv => inv.isLowStock).length,
      outOfStockItems: inventories.filter(inv => inv.currentStock <= 0).length,
      totalCurrentStock: inventories.reduce((sum, inv) => sum + inv.currentStock, 0),
    };

    return {
      summary,
      inventories,
    };
  }

  async createTransaction(
    createTransactionDto: CreateInventoryTransactionDto,
    user: any,
    queryRunner?: QueryRunner,
  ): Promise<InventoryTransaction> {
    const manager = queryRunner ? queryRunner.manager : this.dataSource.manager;

    const inventory = await manager.findOne(Inventory, {
      where: { id: createTransactionDto.inventoryId },
    });

    if (!inventory) {
      throw new NotFoundException('ไม่พบสินค้าคงคลัง');
    }

    // สร้างหมายเลขธุรกรรม
    const transactionNo = await this.generateTransactionNumber();

    const transaction = this.transactionRepository.create({
      ...createTransactionDto,
      transactionNo,
      stockBefore: inventory.currentStock - (createTransactionDto.type === InventoryTransactionType.STOCK_IN ? createTransactionDto.quantity : 0),
      stockAfter: inventory.currentStock + (createTransactionDto.type === InventoryTransactionType.STOCK_OUT ? -createTransactionDto.quantity : 0),
      totalCost: createTransactionDto.unitCost ? createTransactionDto.quantity * createTransactionDto.unitCost : null,
      transactionDate: new Date(),
      inventory: { id: createTransactionDto.inventoryId },
      createdBy: user ? { id: user.id } : null,
    });

    return manager.save(transaction);
  }

  private async generateTransactionNumber(): Promise<string> {
    const today = DateTime.now().toFormat('yyyyMMdd');
    const count = await this.transactionRepository
      .createQueryBuilder('transaction')
      .where('transaction.transactionNo LIKE :pattern', { pattern: `TXN${today}%` })
      .getCount();

    return `TXN${today}${(count + 1).toString().padStart(4, '0')}`;
  }

  async getTransactionHistory(inventoryId: number, user: any): Promise<InventoryTransaction[]> {
    return this.transactionRepository.find({
      where: {
        inventory: {
          id: inventoryId,
          ...(user?.storeId ? { store: { id: user.storeId } } : {}),
        },
      },
      relations: ['inventory', 'inventory.product', 'inventory.branch', 'createdBy'],
      order: { transactionDate: 'DESC' },
    });
  }

  async reserveStock(inventoryId: number, quantity: number, user: any): Promise<Inventory> {
    const inventory = await this.findOne(inventoryId);

    if (inventory.availableStock < quantity) {
      throw new BadRequestException('สต็อกที่พร้อมใช้ไม่เพียงพอสำหรับการจอง');
    }

    inventory.reservedStock += quantity;
    inventory.availableStock -= quantity;
    inventory.lastUpdated = new Date();

    await this.inventoryRepository.save(inventory);
    return inventory;
  }

  async releaseReservedStock(inventoryId: number, quantity: number, user: any): Promise<Inventory> {
    const inventory = await this.findOne(inventoryId);

    if (inventory.reservedStock < quantity) {
      throw new BadRequestException('ไม่สามารถปลดปล่อยสต็อกมากกว่าที่จองไว้ได้');
    }

    inventory.reservedStock -= quantity;
    inventory.availableStock += quantity;
    inventory.lastUpdated = new Date();

    await this.inventoryRepository.save(inventory);
    return inventory;
  }

  /**
   * อัปเดตสต็อกจากใบรับของ (Receipt)
   * @param productId รหัสสินค้า
   * @param branchId รหัสสาขา
   * @param quantity จำนวนที่รับ
   * @param unitCost ต้นทุนต่อหน่วย
   * @param referenceNo หมายเลขอ้างอิง
   * @param user ผู้ใช้งาน
   * @param queryRunner QueryRunner สำหรับ transaction
   */
  async updateStockFromReceipt(
    productId: number,
    branchId: number,
    quantity: number,
    unitCost: number,
    referenceNo: string,
    user: any,
    queryRunner?: QueryRunner,
  ): Promise<Inventory> {
    const manager = queryRunner ? queryRunner.manager : this.inventoryRepository.manager;

    // ค้นหาหรือสร้าง inventory record
    let inventory = await manager.findOne(Inventory, {
      where: {
        product: { id: productId },
        branch: { id: branchId },
        ...(user?.storeId ? { store: { id: user.storeId } } : {}),
      },
      relations: ['product', 'branch', 'store'],
    });

    const stockBefore = inventory?.currentStock || 0;

    if (!inventory) {
      // สร้าง inventory ใหม่
      const product = await manager.findOne(Product, { where: { id: productId } });
      const branch = await manager.findOne(Branch, { where: { id: branchId } });

      inventory = manager.create(Inventory, {
        product,
        branch,
        store: { id: user?.storeId },
        currentStock: quantity,
        availableStock: quantity,
        reservedStock: 0,
        averageCost: unitCost,
        lastCost: unitCost,
        lastUpdated: new Date(),
        active: true,
      });
    } else {
      // อัปเดต inventory ที่มีอยู่
      inventory.currentStock += quantity;
      inventory.availableStock += quantity;

      // คำนวณ weighted average cost
      const totalValue = (stockBefore * inventory.averageCost) + (quantity * unitCost);
      const totalQuantity = stockBefore + quantity;
      inventory.averageCost = totalQuantity > 0 ? totalValue / totalQuantity : unitCost;
      inventory.lastCost = unitCost;
      inventory.lastUpdated = new Date();
    }

    const savedInventory = await manager.save(inventory);

    // สร้าง inventory transaction
    const transactionNo = `RCP-${referenceNo}-${productId}`;
    const transaction = manager.create(InventoryTransaction, {
      transactionNo,
      type: InventoryTransactionType.STOCK_IN,
      quantity,
      unitCost,
      totalCost: quantity * unitCost,
      stockBefore,
      stockAfter: savedInventory.currentStock,
      referenceNo,
      notes: `รับสินค้าจากใบรับของ ${referenceNo}`,
      inventory: savedInventory,
      createdBy: { id: user?.id },
    });

    await manager.save(transaction);
    return savedInventory;
  }

  /**
   * อัปเดตสต็อกจากใบปรับสต็อก (Stock Adjustment)
   * @param inventoryId รหัสสินค้าคงคลัง
   * @param adjustmentQuantity จำนวนที่ปรับ (บวก = เพิ่ม, ลบ = ลด)
   * @param unitCost ต้นทุนต่อหน่วย
   * @param referenceNo หมายเลขอ้างอิง
   * @param reason เหตุผลการปรับ
   * @param user ผู้ใช้งาน
   * @param queryRunner QueryRunner สำหรับ transaction
   */
  async updateStockFromAdjustment(
    inventoryId: number,
    adjustmentQuantity: number,
    unitCost: number,
    referenceNo: string,
    reason: string,
    user: any,
    queryRunner?: QueryRunner,
  ): Promise<Inventory> {
    const manager = queryRunner ? queryRunner.manager : this.inventoryRepository.manager;

    const inventory = await manager.findOne(Inventory, {
      where: { id: inventoryId },
      relations: ['product', 'branch', 'store'],
    });

    if (!inventory) {
      throw new NotFoundException('ไม่พบข้อมูลสินค้าคงคลัง');
    }

    const stockBefore = inventory.currentStock;
    inventory.currentStock += adjustmentQuantity;
    inventory.availableStock = inventory.currentStock - inventory.reservedStock;
    inventory.lastUpdated = new Date();

    // อัปเดต cost หากมีการระบุ
    if (unitCost && unitCost > 0) {
      inventory.lastCost = unitCost;
      // อัปเดต average cost เฉพาะกรณีเพิ่มสต็อก
      if (adjustmentQuantity > 0) {
        const totalValue = (stockBefore * inventory.averageCost) + (adjustmentQuantity * unitCost);
        const totalQuantity = stockBefore + adjustmentQuantity;
        inventory.averageCost = totalQuantity > 0 ? totalValue / totalQuantity : unitCost;
      }
    }

    const savedInventory = await manager.save(inventory);

    // สร้าง inventory transaction
    const transactionType = InventoryTransactionType.ADJUSTMENT;

    const transactionNo = `ADJ-${referenceNo}-${inventory.product.id}`;
    const transaction = manager.create(InventoryTransaction, {
      transactionNo,
      type: transactionType,
      quantity: Math.abs(adjustmentQuantity),
      unitCost,
      totalCost: Math.abs(adjustmentQuantity * unitCost),
      stockBefore,
      stockAfter: savedInventory.currentStock,
      referenceNo,
      notes: `ปรับสต็อก: ${reason}`,
      inventory: savedInventory,
      createdBy: { id: user?.id },
    });

    await manager.save(transaction);
    return savedInventory;
  }



  /**
   * รายงานการเคลื่อนไหวสต็อก
   * @param productId รหัสสินค้า (optional)
   * @param branchId รหัสสาขา (optional)
   * @param startDate วันที่เริ่มต้น (optional)
   * @param endDate วันที่สิ้นสุด (optional)
   * @param user ผู้ใช้งาน
   */
  async getStockMovementReport(
    productId?: number,
    branchId?: number,
    startDate?: string,
    endDate?: string,
    user?: any,
  ): Promise<InventoryTransaction[]> {
    const query = this.transactionRepository
      .createQueryBuilder('transaction')
      .leftJoinAndSelect('transaction.inventory', 'inventory')
      .leftJoinAndSelect('inventory.product', 'product')
      .leftJoinAndSelect('inventory.branch', 'branch')
      .leftJoinAndSelect('transaction.createdBy', 'createdBy');

    if (user?.storeId) {
      query.andWhere('inventory.store.id = :storeId', { storeId: user.storeId });
    }

    if (productId) {
      query.andWhere('product.id = :productId', { productId });
    }

    if (branchId) {
      query.andWhere('branch.id = :branchId', { branchId });
    }

    if (startDate) {
      query.andWhere('transaction.transactionDate >= :startDate', { startDate });
    }

    if (endDate) {
      query.andWhere('transaction.transactionDate <= :endDate', { endDate });
    }

    return query
      .orderBy('transaction.transactionDate', 'DESC')
      .getMany();
  }
}
