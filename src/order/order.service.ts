import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
  forwardRef,
} from '@nestjs/common';
import {
  FilterOperator,
  PaginateConfig,
  PaginateQuery,
  Paginated,
  paginate,
} from 'nestjs-paginate';
import {
  PaymentMethod,
  PaymentMethodType,
} from 'src/payment-method/entities/payment-method.entity';
import { PaymentService } from 'src/payment/payment.service';
import { ProductService } from 'src/product/product.service';
import { Between, DataSource, In, Not, Repository } from 'typeorm';
import { CreateOrderDiscountDto } from './dto/create-order-discount.dto';
import {
  OrderPayment,
  OrderPaymentStatus,
} from './entities/order-payment.entity';
import { Order, OrderStatus } from './entities/order.entity';
import { OrderDiscountRepository } from './repository/order-discount.repository';
import { OrderItemAttributeValueRepository } from './repository/order-item-attribute-value.repository';
import { OrderItemAttributeRepository } from './repository/order-item-attribute.repository';
import { OrderItemRepository } from './repository/order-item.repository';
import { OrderPaymentRepository } from './repository/order-payment.repository';
import { OrderRepository } from './repository/order.repository';
import {
  CreateOrderDto,
  UpdateOrderDto,
  CreateOrderPaymentDto,
} from './dto';
import { DateTime } from 'luxon';
// import { Card, CardType } from 'src/card/entities/card.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Member } from 'src/member/entities/member.entity';
import * as XLSX from 'xlsx';
import { Product } from 'src/product/entities/product.entity';
import { Device } from '../device/entities/device.entity';
import { AuditlogService } from 'src/auditlog/auditlog.service';
import { OrderItem } from './entities/order-item.entity';
import {
  CreateOrderAllinOneDto,
  offlineDto,
  PaynextDto,
} from './dto/create-order-allinone.dto';
import { Branch } from 'src/branch/entities/branch.entity';
import { User } from 'src/user/entities/user.entity';
import { OrderPaidDto } from './dto/order-paid.dto';
import { CreateOrderWithPaymentDto } from './dto/create-order-with-payment.dto';
import { InventoryService } from 'src/inventory/inventory.service';
import { InventoryTransactionType } from 'src/inventory/entities/inventory-transaction.entity';
import { Inventory } from 'src/inventory/entities/inventory.entity';
import { Shift } from 'src/shift/entities/shift.entity';

export const ORDER_PAGINATION_CONFIG: PaginateConfig<Order> = {
  sortableColumns: [
    'id',
    'orderNo',
    'orderDate',
    'orderPayment.amount',
    'orderPayment.status',
  ],
  relations: {
    shift: {
      store: true,
      user: true,
    },
    member: true,
    branch: true,
    orderItems: {
      product: true,
    },
    orderPayment: {
      paymentMethod: true,
    },
    device: true,
  },

  searchableColumns: [
    'orderNo',
    'orderPayment.remark',
    'member.code',
    'member.firstname',
    'member.lastname',
    'member.middlename',
  ],
  defaultSortBy: [['orderDate', 'DESC']],
  filterableColumns: {
    orderDate: [FilterOperator.BTW, FilterOperator.EQ],
    orderStatus: [FilterOperator.EQ, FilterOperator.IN],
    'branch.id': [FilterOperator.EQ],
    'orderPayments.status': [FilterOperator.EQ], // Added filterable column for orderPayments status
    'orderPayments.amount': [FilterOperator.GTE, FilterOperator.LTE], // Added filterable column for orderPayments amount
    'device.id': [FilterOperator.EQ],
  },
};

@Injectable()
export class OrderService {
  constructor(
    private dataSource: DataSource,
    private orderRepository: OrderRepository,
    private orderItemRepository: OrderItemRepository,
    private orderItemAttributeRepository: OrderItemAttributeRepository,
    private orderItemAttributeValueRepository: OrderItemAttributeValueRepository,
    private orderPaymentRepository: OrderPaymentRepository,
    private orderDiscountRepository: OrderDiscountRepository,
    @Inject(AuditlogService)
    private auditlogService: AuditlogService,

    @Inject(forwardRef(() => PaymentService))
    private paymentService: PaymentService,
    @InjectRepository(ProductService)
    private readonly productRepository: Repository<Product>,
    @InjectRepository(Member)
    private readonly memberRepository: Repository<Member>,
    private readonly productService: ProductService,
    @InjectRepository(Device)
    private readonly deviceRepository: Repository<Device>,

    // @Inject(InventoryService)
    private inventoryService: InventoryService,
  ) { }

  async create(createOrderDto: CreateOrderDto, user: any) {
    const userId = user['sub'];
    // const storeId = user['storeId'];

    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const order = Order.create({
        orderNo: await this.orderRepository.generateOrderNum(),
        orderDate: createOrderDto.date,
        total: createOrderDto.total,
        discount: 0,
        orderStatus: OrderStatus.WAIT_PAYMENT,
        grandTotal: createOrderDto.total,
        shift: {
          id: createOrderDto.shiftId,
        },
        member: {
          id: createOrderDto.memberId,
        },
        user: {
          id: userId,
        },
        orderItems: createOrderDto.orderItems.map((itemDto) => {
          return OrderItem.create({
            product: { id: itemDto.productId },
            quantity: itemDto.quantity,
            price: itemDto.price,
            total: itemDto.total,
          });
        }),
        device: {
          id: createOrderDto.deviceId,
        },
      });

      await queryRunner.manager.save(order);

      await queryRunner.commitTransaction();

      return this.findOne(order.id);
    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err)
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }
  }

  findAll() {
    return this.orderRepository.findAll();
  }

  async findOne(id: number) {
    const order = await Order.findOne({
      where: { id: id },
      relations: {
        orderItems: {
          product: {
            category: true,
          },
        },
        orderPayment: {
          paymentMethod: true,
        },
        member: true,
        shift: {
          store: true,
        },
      },
    });

    if (!order) {
      throw new NotFoundException(`Order ${id} not found`);
    }

    return order;
  }

  update(id: number, updateOrderDto: UpdateOrderDto) {
    return `This action updates a #${id} order`;
  }

  remove(id: number) {
    return `This action removes a #${id} order`;
  }

  async datatables(query: PaginateQuery, user: any): Promise<Paginated<Order>> {
    return paginate(
      query,
      this.orderRepository.repository,
      {
        ...ORDER_PAGINATION_CONFIG,
        where: {
          ...(user?.storeId ? { shift: { store: { id: user.storeId } } } : {}),
        }
      },
    );
  }

  async createOrderPayment(orderId: number, dto: CreateOrderPaymentDto) {
    const order = await this.findOne(orderId);
    if (!order) {
      throw new NotFoundException(`Order ${orderId} not found`);
    }

    const oldOpIds = [order.orderPayment.id];

    if (oldOpIds.length) {
      const orderPaymentsToUpdate = oldOpIds.map((id) => ({
        id: id,
        status: OrderPaymentStatus.CANCEL,
      }));
      await this.orderPaymentRepository.repository.save(orderPaymentsToUpdate);
    }
    // if (order.orderStatus != OrderStatus.SELECT_PAYMENT) {
    //     throw new BadRequestException('Invalid status')
    // }

    for (const orderPaymentDto of dto.orderPayments) {

      const orderPaymentData = this.orderPaymentRepository.repository.create({
        amount: orderPaymentDto.amount,
        paymentMethod: { id: orderPaymentDto.paymentMethodId },
        remark: orderPaymentDto.remark,
        order: order,
        status: orderPaymentDto?.status ?? OrderPaymentStatus.WAIT,
      });

      await this.orderPaymentRepository.createOrderPayment(orderPaymentData);
    }

    const grandTotal = order.total - dto?.discount;

    await this.orderRepository.repository.save({
      id: orderId,
      orderStatus: OrderStatus.WAIT_PAYMENT,
      paid: dto?.paid,
      change: dto?.change,
      discount: dto?.discount,
      grandTotal: grandTotal,
    });

    return this.findOne(orderId);
  }

  async createOnlineOrderPayment(orderId: number, orderPaymentId: number) {
    const orderPayment = await this.orderPaymentRepository.repository.findOne({
      where: {
        id: orderPaymentId,
        order: { id: orderId },
      },
      relations: {
        paymentMethod: true,
      },
    });

    if (!orderPayment) {
      throw new NotFoundException(`Order payment ${orderPaymentId} not found`);
    }

    return this.paymentService.createPayment(orderPayment);
  }

  async orderAllPaid(id: number) {
    const order = await this.orderRepository.repository.findOneBy({ id });
    if (!order) {
      throw new NotFoundException(`Order ${id} not found`);
    }

    return this.orderRepository.repository.save({
      id: order.id,
      orderStatus: OrderStatus.COMPLETE,
    });
  }

  async orderPaidWithId(orderId: number, id: number) {
    const orderPayment = await this.orderPaymentRepository.repository.findOneBy(
      { id },
    );
    if (!orderPayment) {
      throw new NotFoundException(`Order payment ${id} not found`);
    }

    orderPayment.status = OrderPaymentStatus.SUCCESS;
    await orderPayment.save();

    await this.isOrderPaidAll(orderId);
  }

  // async orderPaidMemberWithId(
  //   orderId: number,
  //   cardSn: string,
  //   orderPaymentId: number,
  // ) {
  //   const order = await this.orderRepository.repository.findOneBy({
  //     id: orderId,
  //   });
  //   if (!order) {
  //     throw new NotFoundException(`Order ${orderId} not found`);
  //   }

  //   if (order.orderStatus != OrderStatus.WAIT_PAYMENT) {
  //     throw new BadRequestException('Order invalid status');
  //   }

  //   const orderPayment = await OrderPayment.findOneBy({ id: orderPaymentId });
  //   if (!orderPayment) {
  //     throw new NotFoundException(`OrderPayment not found`);
  //   }

  //   if (orderPayment.status != OrderPaymentStatus.WAIT) {
  //     throw new BadRequestException('Order Payment invalid status');
  //   }

  //   const member = await Member.findOne({
  //     where: { sn: cardSn },
  //   });

  //   if (!member) {
  //     throw new NotFoundException(`Member not found`);
  //   }

  //   let grandTotal = order.grandTotal; // Changed from const to let

  //   const queryRunner = this.dataSource.createQueryRunner();

  //   await queryRunner.connect();
  //   await queryRunner.startTransaction();
  //   try {
  //     // Increase member's credit
  //     member.credit += grandTotal;

  //     if (member.cardType == CardType.STUDENT) {
  //       // Handle student-specific logic
  //       const dailyCreditQuota = 45;
  //       // Check if credit exceeds limit
  //       if (member.credit >= member.limitcredit) {
  //         throw new BadRequestException(`Member's credit limit exceeded`);
  //       }
  //       const now = DateTime.now();

  //       let start: Date;
  //       let end: Date;

  //       if (now.hour < 5) {
  //         start = DateTime.now()
  //           .startOf('day')
  //           .minus({ day: 1 })
  //           .plus({ hour: 5 })
  //           .toJSDate();
  //         end = DateTime.now()
  //           .endOf('day')
  //           .minus({ day: 1 })
  //           .plus({ hour: 5 })
  //           .toJSDate();
  //       } else {
  //         start = DateTime.now().startOf('day').plus({ hour: 5 }).toJSDate();
  //         end = DateTime.now().endOf('day').plus({ hour: 5 }).toJSDate();
  //       }

  //       const usedCreditToday = await Transaction.sum('amount', {
  //         date: Between(start, end),
  //         member: {
  //           id: member.id,
  //         },
  //         type: TransactionType.PAID,
  //         channel: TransactionChannel.CARD,
  //         walletType: WalletType.EL2,
  //       });

  //       // Calculate remaining credit
  //       const availableCredit = Math.min(
  //         member.creditEL2,
  //         dailyCreditQuota + usedCreditToday,
  //       );

  //       // Deduct credit from member
  //       const creditUsed = Math.min(availableCredit, grandTotal);
  //       member.creditEL2 -= creditUsed;
  //       grandTotal -= creditUsed;

  //       const transactions: Transaction[] = [];
  //       if (creditUsed > 0) {
  //         const transaction = Transaction.create({
  //           date: new Date(),
  //           amount: -creditUsed,
  //           type: TransactionType.PAID,
  //           channel: TransactionChannel.CARD,
  //           walletType: WalletType.EL2,
  //           description: `Paid with Card amount: ${creditUsed}`,
  //           member: member,
  //           order: order,
  //         });

  //         transactions.push(transaction);
  //       }

  //       await queryRunner.manager.save(transactions);

  //       await queryRunner.manager.save(member);
  //     } else if (
  //       member.cardType == CardType.TEACHER ||
  //       member.cardType == CardType.STAFF
  //     ) {
  //       // Check if the member has enough money
  //       const memberHave = member.wallet;
  //       if (memberHave < grandTotal) {
  //         throw new BadRequestException(`Insufficient balance`);
  //       }

  //       member.wallet = member.wallet - grandTotal;

  //       await queryRunner.manager.save(member);

  //       const transaction = Transaction.create({
  //         date: new Date(),
  //         amount: -grandTotal,
  //         type: TransactionType.PAID,
  //         channel: TransactionChannel.CARD,
  //         walletType: WalletType.WALLET,
  //         description: `Paid with Card amount: ${grandTotal}`,
  //         member: member,
  //         order: order,
  //       });

  //       await queryRunner.manager.save(transaction);
  //     } else {
  //       throw new BadRequestException('Invalid card type');
  //     }

  //     orderPayment.status = OrderPaymentStatus.SUCCESS;
  //     await queryRunner.manager.save(orderPayment);

  //     order.orderStatus = OrderStatus.COMPLETE;
  //     order.member = member;
  //     await queryRunner.manager.save(order);

  //     await this.isOrderPaidAll(orderId);

  //     await queryRunner.commitTransaction();

  //     return this.findOne(orderId);
  //   } catch (err) {
  //     await queryRunner.rollbackTransaction();

  //     throw new BadRequestException(err.message);
  //   } finally {
  //     await queryRunner.release();
  //   }
  // }

  // async orderPaidMemberWithCard(
  //   orderId: number,
  //   cardSn: string,
  //   orderPaymentId: number,
  // ) {
  //   const order = await this.orderRepository.repository.findOneBy({
  //     id: orderId,
  //   });
  //   if (!order) {
  //     throw new NotFoundException(`Order ${orderId} not found`);
  //   }

  //   if (order.orderStatus != OrderStatus.WAIT_PAYMENT) {
  //     throw new BadRequestException('Order invalid status');
  //   }

  //   const orderPayment = await OrderPayment.findOneBy({ id: orderPaymentId });
  //   if (!orderPayment) {
  //     throw new NotFoundException(`OrderPayment not found`);
  //   }

  //   if (orderPayment.status != OrderPaymentStatus.WAIT) {
  //     throw new BadRequestException('Order Payment invalid status');
  //   }

  //   const member = await Member.findOne({
  //     where: { sn: cardSn },
  //   });

  //   if (!member) {
  //     throw new NotFoundException(`Member not found`);
  //   }
  //   if (!member.active) {
  //     throw new BadRequestException(`Member is inactive`);
  //   }

  //   const grandTotal = order.grandTotal; // Changed from const to let

  //   if (member.credit + grandTotal > member.limitcredit) {
  //     throw new BadRequestException(`Member's credit limit exceeded`);
  //   }

  //   const queryRunner = this.dataSource.createQueryRunner();
  //   await queryRunner.connect();
  //   await queryRunner.startTransaction();
  //   try {
  //     const transaction = Transaction.create({
  //       date: new Date(),
  //       amount: -grandTotal,
  //       type: TransactionType.PAID,
  //       channel: TransactionChannel.CARD,
  //       walletType: WalletType.CREDIT,
  //       description: `Paid with Card amount: ${grandTotal}`,
  //       member: member,
  //       order: order,
  //     });

  //     await queryRunner.manager.save(transaction);
  //     member.credit += grandTotal;
  //     await this.memberRepository.save(member);

  //     orderPayment.status = OrderPaymentStatus.SUCCESS;
  //     await queryRunner.manager.save(orderPayment);

  //     order.orderStatus = OrderStatus.COMPLETE;
  //     order.member = member;
  //     await queryRunner.manager.save(order);

  //     await this.isOrderPaidAll(orderId);
  //     await queryRunner.commitTransaction();

  //     const data = this.findOne(orderId);

  //     return data;
  //   } catch (err) {
  //     await queryRunner.rollbackTransaction();

  //     throw new BadRequestException(err.message);
  //   } finally {
  //     await queryRunner.release();
  //   }
  // }

  async isOrderPaidAll(orderId: number) {
    const orderAndCount =
      await this.orderPaymentRepository.repository.findAndCount({
        where: {
          order: { id: orderId },
          status: Not(OrderPaymentStatus.SUCCESS),
        },
      });

    if (orderAndCount[1] == 0) {
      await this.orderRepository.repository.update(orderId, {
        orderStatus: OrderStatus.COMPLETE,
      });
    }
  }

  async createNextPayment(orderId: number) {
    const order = await this.findOne(orderId);
    if (!order) {
      throw new NotFoundException(`Order ${orderId} not found`);
    }

    const orderPayment = await this.orderPaymentRepository.repository.findOne({
      relations: { paymentMethod: true },
      where: {
        order: { id: orderId },
        status: OrderPaymentStatus.WAIT,
      },
      order: { createdAt: 'ASC' },
    });

    if (!orderPayment) {
      return {
        next: false,
        orderPayment: null,
        payment: null,
      };
    }

    let payment = null;
    // if (orderPayment.paymentMethod.type == PaymentMethodType.THAIQR) {
    //   payment = await this.paymentService.createPayment(orderPayment);
    // }

    return {
      next: true,
      orderPayment,
      payment,
    };
  }

  async report(startDate: Date, endDate: Date) {
    return this.orderRepository.repository.find({
      relations: {
        shift: {
          user: true,
          store: true,
        },
        orderPayment: {
          paymentMethod: true,
        },
      },
      where: {
        orderDate: Between(startDate, endDate),
      },
    });
  }

  async reportOrderToday() {
    const now = DateTime.now();
    const start = now.startOf('day').toJSDate();
    const end = now.endOf('day').toJSDate();

    return this.orderRepository.repository.find({
      relations: {
        member: true,
      },
      where: {
        orderDate: Between(start, end),
      },
    });
  }

  async getOrderByCode(orderNo: string) {
    const order = await this.orderRepository.repository.findOne({
      where: {
        orderNo: orderNo,
      },
      relations: {
        orderItems: {
          product: {
            category: true,
          },
          attributes: {
            attributeValues: true,
          },
        },
        orderPayment: {
          paymentMethod: true,
        },
      },
    });

    if (!order) {
      throw new NotFoundException(`Order ${orderNo} not found`);
    }

    return order;
  }

  // async voidBill(id: number, userId: number) {
  //   const queryRunner = this.dataSource.createQueryRunner();
  //   await queryRunner.connect();
  //   await queryRunner.startTransaction();
  //   try {
  //     const order = await this.orderRepository.repository.findOne({
  //       where: { id: id },
  //       // relations: ['member', 'orderPayments', 'orderPayments.paymentMethod'],
  //       relations: {
  //         member: true,
  //         orderPayments: {
  //           paymentMethod: true,
  //         },
  //         orderItems: {
  //           product: true,
  //           attributes: {
  //             attributeValues: true,
  //           },
  //         },
  //         branch: true,
  //         device: true,
  //       },
  //     });

  //     if (!order) {
  //       throw new NotFoundException('Order not found');
  //     }

  //     if (!order.member) {
  //       throw new NotFoundException('Order has no associated member');
  //     }
  //     if (order.orderStatus != OrderStatus.COMPLETE) {
  //       throw new BadRequestException('This order is already void');
  //     }
  //     const voidNo = '2' + order.orderNo.slice(1);
  //     const check = await this.orderRepository.repository.find({
  //       where: { orderNo: voidNo },
  //     });
  //     if (check.length > 0) {
  //       throw new BadRequestException('ไม่สามารถ Void ได้');
  //     }
  //     const op = await OrderPayment.findOne({
  //       where: {
  //         order: {
  //           id: id,
  //         },
  //         paymentMethod: {
  //           type: PaymentMethodType.MEMBER,
  //         },
  //         status: OrderPaymentStatus.SUCCESS,
  //       },
  //     });
  //     if (!op) {
  //       throw new BadRequestException('Paymentmedthod is not Member');
  //     }

  //     // const order = await this.orderRepository.findOne(id);
  //     // const paymentMethod = order.orderPayments[0]?.paymentMethod;

  //     // if (paymentMethodIds !== 4) {
  //     //     throw new BadRequestException("Cannot void order with this payment method");
  //     // }

  //     const member = await this.memberRepository.findOneBy({
  //       id: order.member.id,
  //     });

  //     if (!member) {
  //       throw new NotFoundException('Member not found');
  //     }

  //     const orderVoid = this.orderRepository.repository.create({
  //       orderNo: voidNo,
  //       total: order.total,
  //       device: { id: order.device.id },
  //       remark: order.remark,
  //       change: order.change,
  //       discount: order.discount,
  //       orderType: OrderType.ORDER,
  //       orderDate: new Date(),
  //       branch: { id: order.branch.id },
  //       paid: order.paid,
  //       orderStatus: OrderStatus.VOID,
  //       member: order.member,
  //       grandTotal: order.grandTotal * -1,
  //       uuid: 'Void' + order.uuid,
  //       user: { id: userId },
  //     });
  //     await queryRunner.manager.save(orderVoid);
  //     // await this.orderRepository.repository.update(order.id, {
  //     //     orderPayments: OrderStatus.COMPLETE,
  //     // });

  //     const paymentVoid = this.orderPaymentRepository.repository.create({
  //       createdAt: new Date(),
  //       updatedAt: new Date(),
  //       amount: orderVoid.grandTotal,
  //       remark: 'Void',
  //       status: OrderPaymentStatus.SUCCESS,
  //       paymentMethod: { id: order.orderPayments[0].paymentMethod.id },
  //       order: { id: orderVoid.id },
  //     });
  //     await queryRunner.manager.save(paymentVoid);

  //     for (const item of order.orderItems) {
  //       const clonedItem = await this.orderItemRepository.repository.create({
  //         order: { id: orderVoid.id },
  //         createdAt: new Date(),
  //         updatedAt: new Date(),
  //         product: item.product,
  //         quantity: item.quantity * -1,
  //         price: item.price,
  //         total: item.total * -1,
  //       });

  //       const savedClonedItem = await queryRunner.manager.save(clonedItem);
  //       if (item.attributes) {
  //         for (const attr of item.attributes) {
  //           const clonedAttribute =
  //             await this.orderItemAttributeRepository.repository.create({
  //               orderItem: { id: savedClonedItem.id },
  //               attributeName: attr.attributeName,
  //               total: attr.total,
  //               createdAt: new Date(),
  //               updatedAt: new Date(),
  //             });

  //           const savedClonedAttribute =
  //             await this.orderItemAttributeRepository.repository.save(
  //               clonedAttribute,
  //             );

  //           if (attr.attributeValues) {
  //             for (const val of attr.attributeValues) {
  //               const clonedAttributeValue =
  //                 await this.orderItemAttributeValueRepository.repository.create(
  //                   {
  //                     orderItemAttribute: { id: savedClonedAttribute.id },
  //                     price: val.price,
  //                     total: val.total,
  //                     quantity: val.quantity,
  //                     createdAt: new Date(),
  //                     updatedAt: new Date(),
  //                   },
  //                 );

  //               await queryRunner.manager.save(clonedAttributeValue);
  //             }
  //           }
  //         }
  //       }
  //     }

  //     const grandTotal = order.total;
  //     member.credit -= grandTotal;
  //     await this.memberRepository.save(member);

  //     const auditLog = new AuditLog();
  //     auditLog.action = 'VOID_BILL';
  //     auditLog.description = `Order ${order.orderNo} voided by user ${userId}`;
  //     auditLog.timestamp = new Date();
  //     auditLog.member = member;
  //     auditLog.userId = userId;
  //     await this.auditlogService.create(auditLog);

  //     const transaction = Transaction.create({
  //       date: new Date(),
  //       amount: grandTotal,
  //       type: TransactionType.VOID,
  //       channel: TransactionChannel.CARD,
  //       walletType: WalletType.CREDIT,
  //       description: `Void with Order amount: ${grandTotal}`,
  //       member: member,
  //       order: orderVoid,
  //     });

  //     await queryRunner.manager.save(transaction);
  //     await queryRunner.commitTransaction();
  //     return orderVoid;
  //   } catch (err) {
  //     await queryRunner.rollbackTransaction();

  //     throw new BadRequestException(err.message);
  //   } finally {
  //     await queryRunner.release();
  //   }
  // }

  async createOrderDiscount(id: number, dto: CreateOrderDiscountDto) {
    await this.orderDiscountRepository.create(id, dto.orderDiscounts);

    // await this.orderRepository.updateDiscount(id, dto.orderDiscounts[0].amount)
  }

  // async import(file: Express.Multer.File, userId: number) {
  //   const HEADERS = ['memberCode', 'productCode', 'productname'];

  //   const workbook = XLSX.read(file.buffer, { type: 'buffer' });
  //   const sheetName = workbook.SheetNames[0];
  //   const sheet = workbook.Sheets[sheetName];
  //   const jsonData = XLSX.utils.sheet_to_json(sheet, { header: 1 });

  //   const actualHeaders: string[] = jsonData[0] as Array<string>;
  //   const isValid = HEADERS.every((header) => actualHeaders.includes(header));

  //   if (!isValid) {
  //     throw new BadRequestException('Header validation failed.');
  //   }

  //   const result = {
  //     create: 0,
  //     update: 0,
  //     ok: 0,
  //     error: 0,
  //     errorDetails: [],
  //   };

  //   const products = await this.productService.findAllforOrder();
  //   const productMap = new Map(
  //     products.map((product) => [product.code, product]),
  //   );

  //   const queryRunner = this.dataSource.createQueryRunner();

  //   await queryRunner.connect();
  //   await queryRunner.startTransaction();
  //   try {
  //     const orderNumberStr = await this.orderRepository.generateOrderNum();
  //     const OrderNo = Number(orderNumberStr);
  //     let OrderNopre = OrderNo - 1;

  //     for (let rowIndex = 1; rowIndex < jsonData.length; rowIndex++) {
  //       // Start from 1 assuming first row is headers
  //       const row = jsonData[rowIndex];
  //       try {
  //         const memberCode = String(
  //           row[actualHeaders.indexOf('memberCode')],
  //         ).trim();
  //         const productCode = String(
  //           row[actualHeaders.indexOf('productCode')],
  //         ).trim();

  //         // Use today's date and time, correctly set in the desired timezone
  //         const date = DateTime.now().setZone('Asia/Bangkok').toJSDate();

  //         const member = await this.memberRepository.findOne({
  //           where: { code: ILike(`%${memberCode}%`) },
  //         });
  //         if (!member) {
  //           throw new Error(`Member with code ${memberCode} not found`);
  //         }

  //         const deviceCode = 'WEB001';
  //         const device = await this.deviceRepository.findOneBy({
  //           code: deviceCode,
  //         });

  //         const product = await this.productService.findByCode(productCode);
  //         if (!product) {
  //           throw new Error(`Product with code ${productCode} not found`);
  //         }

  //         if (member.credit + product.price >= member.limitcredit) {
  //           throw new BadRequestException(
  //             `This user:${memberCode} limit exceed`,
  //           );
  //         }

  //         OrderNopre += 1;
  //         const OrderNoStr = OrderNopre.toString();

  //         const existingProduct = await queryRunner.manager.findOne(Product, {
  //           where: { id: product.id },
  //         });

  //         if (existingProduct) {
  //           const order = queryRunner.manager.create(Order, {
  //             orderNo: OrderNoStr,
  //             total: existingProduct.price,
  //             device: device,
  //             remark: '',
  //             orderType: OrderType.RESERVE,
  //             memberId: member.id,
  //             orderDate: date, // Use today's date and time
  //             branchId: null,
  //             paid: existingProduct.price,
  //             orderStatus: OrderStatus.COMPLETE,
  //             member: member,
  //             grandTotal: existingProduct.price,
  //             orderItems: [
  //               {
  //                 product: existingProduct,
  //                 price: existingProduct.price,
  //                 quantity: 1,
  //                 total: existingProduct.price,
  //                 attributes: [],
  //               },
  //             ],
  //           });

  //           await queryRunner.manager.save(order);

  //           const paymentMethod = await PaymentMethod.findOne({
  //             where: {
  //               type: PaymentMethodType.MEMBER,
  //             },
  //           });

  //           const reserveOrderPayment = queryRunner.manager.create(
  //             OrderPayment,
  //             {
  //               paymentMethod: paymentMethod,
  //               amount: product.price,
  //               remark: '',
  //               status: OrderPaymentStatus.SUCCESS,
  //               order: order,
  //             },
  //           );

  //           await queryRunner.manager.save(reserveOrderPayment);

  //           const transaction = queryRunner.manager.create(Transaction, {
  //             date: date, // Use today's date and time
  //             amount: -product.price,
  //             type: TransactionType.PAID,
  //             channel: TransactionChannel.CARD,
  //             walletType: WalletType.CREDIT,
  //             description: `Paid with Card amount: ${product.price}`,
  //             member: member,
  //             order: order,
  //           });

  //           member.credit += product.price;

  //           await this.memberRepository.save(member);
  //           await queryRunner.manager.save(transaction);

  //           result.create += 1;
  //           result.ok += 1;
  //         }
  //       } catch (error) {
  //         result.error += 1;
  //         result.errorDetails.push({
  //           row: rowIndex + 1,
  //           error: `on row ${rowIndex + 1}, ${error.message}`,
  //         });
  //       }
  //     }

  //     await queryRunner.commitTransaction();

  //     return result;
  //   } catch (err) {
  //     await queryRunner.rollbackTransaction();
  //     throw new Error(`Import failed: ${err.message}`);
  //   } finally {
  //     await queryRunner.release();
  //   }
  // }

  // async orderPaidMemberWithcode(
  //   orderId: number,
  //   memberCode: string,
  //   orderPaymentId: number,
  // ) {
  //   const order = await this.orderRepository.repository.findOneBy({
  //     id: orderId,
  //   });
  //   const member = await this.memberRepository.findOneBy({ code: memberCode });
  //   if (!member) {
  //     throw new Error(`Member with code ${memberCode} not found`);
  //   }
  //   if (!member.active) {
  //     throw new BadRequestException(`Member is inactive`);
  //   }
  //   if (!order) {
  //     throw new NotFoundException(`Order ${orderId} not found`);
  //   }
  //   if (order.orderStatus != OrderStatus.WAIT_PAYMENT) {
  //     throw new BadRequestException('Order invalid status');
  //   }

  //   const orderPayment = await OrderPayment.findOneBy({ id: orderPaymentId });
  //   if (orderPayment.status != OrderPaymentStatus.WAIT) {
  //     throw new BadRequestException('Order Payment invalid status');
  //   }

  //   const grandTotal = order.grandTotal; // Changed from const to let
  //   if (member.credit + grandTotal >= member.limitcredit) {
  //     throw new BadRequestException(`Member's credit limit exceeded`);
  //   }

  //   const queryRunner = this.dataSource.createQueryRunner();

  //   await queryRunner.connect();
  //   await queryRunner.startTransaction();
  //   try {
  //     // Update member credit
  //     member.credit += grandTotal;
  //     const transaction = Transaction.create({
  //       date: new Date(),
  //       amount: -grandTotal,
  //       type: TransactionType.PAID,
  //       channel: TransactionChannel.CARD,
  //       walletType: WalletType.CREDIT,
  //       description: `Paid with Card amount: ${grandTotal}`,
  //       member: member,
  //       order: order,
  //     });
  //     await queryRunner.manager.save(transaction);
  //     // Save changes in a transaction
  //     await this.memberRepository.save(member);
  //     await queryRunner.commitTransaction();

  //     orderPayment.status = OrderPaymentStatus.SUCCESS;
  //     await queryRunner.manager.save(orderPayment);

  //     order.orderStatus = OrderStatus.COMPLETE;
  //     order.member = member;
  //     await queryRunner.manager.save(order);
  //     // Return member information
  //     return member;
  //   } catch (error) {
  //     // Rollback transaction in case of error
  //     await queryRunner.rollbackTransaction();
  //     throw error;
  //   } finally {
  //     // Release the query runner
  //     await queryRunner.release();
  //   }
  // }

  // if (!order) {
  //   throw new NotFoundException(`Order ${orderId} not found`);
  // }

  // if (order.orderStatus != OrderStatus.WAIT_PAYMENT) {
  //   throw new BadRequestException('Order invalid status');
  // }

  // const orderPayment = await OrderPayment.findOneBy({ id: orderPaymentId });
  // if (!orderPayment) {
  //   throw new NotFoundException(`OrderPayment not found`);
  // }

  // if (orderPayment.status != OrderPaymentStatus.WAIT) {
  //   throw new BadRequestException('Order Payment invalid status');
  // }

  async exportTemplate(): Promise<Buffer> {
    // Create a new workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheetData = [
      ['memberCode', 'productCode', 'productname'],
      ['Example: A1', 'Example:A1', 'Example:Apple'],
    ];

    // Convert worksheet data to sheet
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

    // Append the worksheet to the workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Reserve Template');

    // Write the workbook to a buffer
    const fileBuffer = XLSX.write(workbook, {
      type: 'buffer',
      bookType: 'xlsx',
    });

    return fileBuffer;
  }

  async AllinOneOrder(
    createOrderAllinOneDto: CreateOrderAllinOneDto,
    userId: number,
  ) {
    const productIds = createOrderAllinOneDto.orderItems.map(
      (item) => item.productId,
    );
    const products = await Product.find({
      where: { id: In(productIds) },
    });
    if (products.length !== productIds.length) {
      throw new BadRequestException('One or more products do not exist');
    }

    const user = await User.findOneBy({ id: userId });
    if (!user) {
      throw new BadRequestException('User not found');
    }

    const branch = await Branch.findOneBy({
      id: createOrderAllinOneDto.branchId,
    });
    if (!branch) {
      throw new BadRequestException('Branch not found');
    }

    const device = await Device.findOneBy({
      id: createOrderAllinOneDto.deviceId,
    });
    if (!device) {
      throw new BadRequestException('Device not found');
    }

    let member = null;
    if (createOrderAllinOneDto.member) {
      member = await this.memberRepository.findOneBy({
        sn: createOrderAllinOneDto.member,
      });
      if (!member) {
        throw new BadRequestException('Member not found');
      } else if (
        member.credit + createOrderAllinOneDto.total >
        member.limitcredit
      ) {
        throw new BadRequestException(
          `Insufficient Balance Code : ${member.code} Name : ${member.fullName} Balance : ${member.remainCredit}`,
        );
      }
    }

    const newOrderNum = await this.orderRepository.generateOrderNum();
    const order = new Order();
    order.orderDate = new Date();
    order.orderStatus = OrderStatus.WAIT_PAYMENT;
    order.total = createOrderAllinOneDto.total;
    order.remark = createOrderAllinOneDto.remark;
    order.paid = createOrderAllinOneDto.paid ?? 0;
    order.change = createOrderAllinOneDto.change ?? 0;
    order.discount = createOrderAllinOneDto.discount ?? 0;
    order.grandTotal =
      createOrderAllinOneDto.total - createOrderAllinOneDto.discount;
    order.user = user;
    order.device = device;
    order.branch = branch;
    order.member = member;
    order.orderNo = newOrderNum;

    // Handle OrderItems
    const orderItems = createOrderAllinOneDto.orderItems.map((itemDto) => {
      const product = products.find((p) => p.id === itemDto.productId);
      const orderItem = new OrderItem();
      orderItem.product = product;
      orderItem.quantity = itemDto.quantity;
      orderItem.price = itemDto.price;
      orderItem.total = itemDto.total;

      // Handle OrderItem Attributes
      // const orderItemAttributes =
      //   itemDto.attributes?.map((attributeDto) => {
      //     const orderItemAttribute = new OrderItemAtribute();
      //     orderItemAttribute.attributeName = attributeDto.attributeName;
      //     orderItemAttribute.total = attributeDto.total;

      //     // Handle OrderItem Attribute Values
      //     const orderItemAttributeValues = attributeDto.attributeValues?.map(
      //       (valueDto) => {
      //         return new OrderItemAtributeValue({
      //           price: valueDto.price,
      //           total: valueDto.total,
      //           quantity: valueDto.quantity,
      //           orderItemAttribute: orderItemAttribute,
      //         });
      //       },
      //     );

      //     orderItemAttribute.attributeValues = orderItemAttributeValues;
      //     return orderItemAttribute;
      //   }) || [];

      // orderItem.attributes = orderItemAttributes;
      return orderItem;
    });

    order.orderItems = orderItems;

    // Fetch Payment Methods
    const paymentMethodIds = createOrderAllinOneDto.orderPayments.map(
      (payment) => payment.paymentMethodId,
    );
    const paymentMethods = await PaymentMethod.find({
      where: { id: In(paymentMethodIds) },
    });

    // Handle OrderPayments
    if (createOrderAllinOneDto.orderPayments) {
      // order.orderPayment = createOrderAllinOneDto.orderPayment.map(
      //   (paymentDto) => {
      //     const paymentMethod = paymentMethods.find(
      //       (pm) => pm.id === paymentDto.paymentMethodId,
      //     );

      //     return this.orderPaymentRepository.repository.create({
      //       amount: paymentDto.amount,
      //       paymentMethod: paymentMethod,
      //       remark: paymentDto.remark,
      //       status: paymentDto?.status ?? OrderPaymentStatus.WAIT,
      //       order: order,
      //     });
      //   },
      // );
    }
    const savedOrder = await this.orderRepository.repository.save(order);
    // Fetch and Return Final Data
    const finalData = await this.orderRepository.repository.findOne({
      where: { id: savedOrder.id },
      relations: {
        orderItems: true,
        orderPayment: true,
        member: true,
      },
      select: {
        id: true,
        orderItems: true,
        orderPayment: {
          id: true,
        },
        member: {
          // Ensure member fields are included in select
          id: true,
          sn: true,
          credit: true,
          limitcredit: true,
        },
      },
    });

    return finalData;
  }

  async Paynext(paynextDto: PaynextDto, userId: number) {
    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Fetch the order
      const order = await queryRunner.manager.findOne(Order, {
        where: { id: paynextDto.orderId },
        // Note: Removed orderStatus check if it's not needed
      });

      if (!order) {
        throw new BadRequestException('Order not found or invalid status.');
      }

      // Fetch the payment
      const payment = await queryRunner.manager.findOne(OrderPayment, {
        where: { id: paynextDto.orderpaymentId },
        relations: { paymentMethod: true },
      });

      if (!payment) {
        throw new BadRequestException('Order payment not found.');
      }
      let member = null;

      if (paynextDto.memberSn && paynextDto.membercode) {
        throw new BadRequestException(
          'Please choose either Member Code or Member Card SN, not both',
        );
      } else if (paynextDto.memberSn && paynextDto.membercode == null) {
        member = await queryRunner.manager.findOne(Member, {
          where: { sn: paynextDto.memberSn },
        });
        if (!member) {
          throw new BadRequestException('Member Not Found.');
        }
      } else if (paynextDto.membercode && paynextDto.memberSn == null) {
        member = await queryRunner.manager.findOne(Member, {
          where: { code: paynextDto.membercode },
        });
        if (!member) {
          throw new BadRequestException('Member Not Found.');
        }
      }

      // if (payment.paymentMethod.type === PaymentMethodType.MEMBER) {
      //   if (member) {
      //     if (member.credit + order.grandTotal > member.limitcredit) {
      //       throw new BadRequestException(
      //         `Insufficient Balance Code : ${member.code} Name : ${member.fullName} Balance : ${member.remainCredit}`,
      //       );
      //     }

      //     member.credit += order.grandTotal;
      //     await queryRunner.manager.save(member);

      //     payment.status = OrderPaymentStatus.SUCCESS;
      //     await queryRunner.manager.save(payment);

      //     order.orderStatus = OrderStatus.COMPLETE;
      //     order.member = member;
      //     await queryRunner.manager.save(order);
      //   } else {
      //     throw new BadRequestException(
      //       'Member Card-SN or Member Code is required for MEMBER payment type.',
      //     );
      //   }
      // } else 
      if (payment.paymentMethod.type === PaymentMethodType.CASH) {
        payment.status = OrderPaymentStatus.SUCCESS;
        await queryRunner.manager.save(payment);
      } else {
        throw new BadRequestException('Unsupported payment method type.');
      }

      const unpaidPaymentsCount = await queryRunner.manager.count(
        OrderPayment,
        {
          where: {
            order: { id: order.id },
            status: Not(OrderPaymentStatus.SUCCESS),
          },
        },
      );

      if (unpaidPaymentsCount === 0) {
        order.orderStatus = OrderStatus.COMPLETE;
        await queryRunner.manager.save(order);
      }

      await queryRunner.commitTransaction();

      const user = await queryRunner.manager.findOne(User, {
        where: { id: userId },
      });

      const result = await queryRunner.manager.findOne(Order, {
        where: { id: paynextDto.orderId },
        relations: {
          device: true,
          branch: true,
          member: {
          },
          orderItems: {
            product: true,
            attributes: {
              attributeValues: true,
            },
          },
          orderPayment: { paymentMethod: true },
        },
      });

      let finalresult = null;
      if (result) {
        finalresult = {
          id: result.id,
          orderNo: result.orderNo,
          orderDate: result.orderDate,
          orderStatus: result.orderStatus,
          grandTotal: result.grandTotal,
          orderitems: result.orderItems.map((orderItem) => ({
            orderItemId: orderItem.id,
            product: orderItem.product,
            attributes: orderItem.attributes
              ? orderItem.attributes.map((attribute) => ({
                attributeId: attribute.id,
                attributeName: attribute.attributeName,
                attributeValues: attribute.attributeValues
                  ? attribute.attributeValues.map((value) => value)
                  : [],
              }))
              : [],
          })),
          total: result.total,
          deviceName: result.device?.name,
          branchName: result.branch?.name,
          memberFirstname: result.member?.fullName,
          memberSn: result.member?.sn,
          memberCredit: result.member?.remainCredit,
          member: result.member?.code,
          paymentMethodName: result.orderPayment?.paymentMethod?.name,
          user: user?.fullName,
        };
      }
      return finalresult;
    } catch (err) {
      console.log(err);
      await queryRunner.rollbackTransaction();
      throw new BadRequestException(err.message);
    } finally {
      await queryRunner.release();
    }
  }

  async offlineMode(offlineDtos: offlineDto[], userId: number) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    const newOrderNum = await this.orderRepository.generateOrderNum();
    const OrderNo = Number(newOrderNum);
    let OrderNopre = OrderNo - 1;

    try {
      const result = [];
      const uuid: string[] = [];

      for (const offlinedto of offlineDtos) {
        const validationErrors = [];

        // Validate branch and device existence
        const user = await User.findOneBy({ id: userId });
        if (!user) {
          validationErrors.push('User not found');
        }
        const productIds = offlinedto.orderItems.map((item) => item.productId);
        const products = await Product.find({
          where: { id: In(productIds) },
        });
        if (products.length !== productIds.length) {
          validationErrors.push('One or more products do not exist');
        }
        const branch = await queryRunner.manager.findOne(Branch, {
          where: { id: offlinedto.branchId },
        });
        if (!branch)
          validationErrors.push(
            `Branch with ID ${offlinedto.branchId} not found`,
          );

        const device = await queryRunner.manager.findOne(Device, {
          where: { id: offlinedto.deviceId },
        });
        if (!device)
          validationErrors.push(
            `Device with ID ${offlinedto.deviceId} not found`,
          );

        // Handle member ID validation
        let member = null;

        if (offlinedto.memberSn && offlinedto.membercode) {
          validationErrors.push(
            'Please choose either Member Code or Member Card SN, not both',
          );
        } else if (offlinedto.memberSn && offlinedto.membercode == null) {
          member = await queryRunner.manager.findOne(Member, {
            where: { sn: offlinedto.memberSn },
          });
          if (!member) {
            validationErrors.push('Member Not Found.');
          }
        } else if (offlinedto.membercode && offlinedto.memberSn == null) {
          member = await queryRunner.manager.findOne(Member, {
            where: { code: offlinedto.membercode },
          });
          if (!member) {
            validationErrors.push('Member Not Found.');
          }
        }

        if (validationErrors.length > 0) {
          // If there are validation errors, add an incomplete status with reasons
          result.push({
            status: 'incomplete',
            reason: validationErrors.join(', '),
          });
          continue; // Skip to the next DTO in the loop
        }
        OrderNopre += 1;
        const OrderNoStr = OrderNopre.toString();

        const grandTotal = offlinedto.total - (offlinedto.discount ?? 0);
        const newOrder = this.orderRepository.repository.create({
          total: offlinedto.total,
          orderDate: offlinedto.orderdate,
          orderStatus: OrderStatus.WAIT_PAYMENT,
          device: device,
          branch: branch,
          member: member,
          remark: offlinedto.remark,
          paid: offlinedto.paid ?? 0,
          change: offlinedto.change ?? 0,
          discount: offlinedto.discount ?? 0,
          grandTotal: grandTotal,
          user: user,
          orderNo: OrderNoStr,
        });

        // Save the new order
        const savedOrder = await queryRunner.manager.save(newOrder);
        if (!savedOrder || !savedOrder.id) {
          result.push({
            status: 'incomplete',
            reason: 'Failed to save order',
          });
          continue;
        }

        // Handle order items
        if (offlinedto.orderItems) {
          for (const itemDto of offlinedto.orderItems) {
            const orderItem = OrderItem.create({
              order: { id: savedOrder.id } as Order,
              product: { id: itemDto.productId } as Product,
              quantity: itemDto.quantity,
              price: itemDto.price,
              total: itemDto.total,
            });

            await queryRunner.manager.save(orderItem);

            // if (itemDto.attributes && itemDto.attributes.length > 0) {
            //   for (const attributeDto of itemDto.attributes) {
            //     const orderItemAttribute = queryRunner.manager.create(
            //       OrderItemAtribute,
            //       {
            //         orderItem: orderItem,
            //         attributeName: attributeDto.attributeName,
            //         total: attributeDto.total,
            //       },
            //     );

            //     const savedOrderItemAttribute =
            //       await queryRunner.manager.save(orderItemAttribute);

            //     if (
            //       attributeDto.attributeValues &&
            //       attributeDto.attributeValues.length > 0
            //     ) {
            //       for (const valueDto of attributeDto.attributeValues) {
            //         const orderItemAttributeValue = queryRunner.manager.create(
            //           OrderItemAtributeValue,
            //           {
            //             orderItemAttribute: savedOrderItemAttribute,
            //             value: valueDto.attributeValueName,
            //             quantity: valueDto.quantity,
            //             price: valueDto.price,
            //             total: valueDto.total,
            //           },
            //         );

            //         await queryRunner.manager.save(orderItemAttributeValue);
            //       }
            //     }
            //   }
            // }
          }
        }

        // Handle order payments
        if (offlinedto.orderPayments) {
          for (const paymentDto of offlinedto.orderPayments) {
            const paymentMethod = await queryRunner.manager.findOne(
              PaymentMethod,
              { where: { id: paymentDto.paymentMethodId } },
            );
            if (!paymentMethod) {
              result.push({
                status: 'incomplete',
                reason: `Payment method with ID ${paymentDto.paymentMethodId} not found`,
              });
              continue;
            }

            const orderPayment = this.dataSource.manager.create(OrderPayment, {
              amount: paymentDto.amount,
              paymentMethod: paymentMethod,
              remark: paymentDto.remark,
              order: savedOrder,
              status: paymentDto.status ?? OrderPaymentStatus.WAIT,
            });

            const savedOrderPayment =
              await queryRunner.manager.save(orderPayment);

            if (paymentMethod.type === PaymentMethodType.CASH) {
              await queryRunner.manager.update(
                OrderPayment,
                { id: savedOrderPayment.id },
                {
                  status: OrderPaymentStatus.SUCCESS,
                },
              );

              await queryRunner.manager.update(Order, savedOrder.id, {
                orderStatus: OrderStatus.COMPLETE,
              });
            }
            // else if (paymentMethod.type === PaymentMethodType.MEMBER) {
            //   if (member) {
            //     // if (member.credit + savedOrder.grandTotal > member.limitcredit) {
            //     //   result.push({ status: 'incomplete', reason: 'Credit limit exceeded. Please contact staff.', uuid: offlinedto.uuid });
            //     //   continue;
            //     // } else {
            //     member.credit = member.credit + savedOrder.grandTotal;
            //     await queryRunner.manager.save(member);
            //     await queryRunner.manager.update(Order, savedOrder.id, {
            //       orderStatus: OrderStatus.COMPLETE,
            //     });
            //     await queryRunner.manager.update(
            //       OrderPayment,
            //       { id: savedOrderPayment.id },
            //       {
            //         status: OrderPaymentStatus.SUCCESS,
            //       },
            //     );
            //     // }
            //   } else {
            //     result.push({
            //       status: 'incomplete',
            //       reason:
            //         'Member Card-SN or Member Code is required for MEMBER payment type.',
            //       uuid: offlinedto.uuid,
            //     });
            //   }
            // } 
            else {
              result.push({
                status: 'incomplete',
                reason: 'Unsupported payment method type',
              });
              continue;
            }
          }
        }

        result.push({ status: 'Complete', reason: '' });
      }

      await queryRunner.commitTransaction();

      return result;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async orderPaid(orderId: number, dto: OrderPaidDto) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const order = await queryRunner.manager.findOne(Order, {
        where: { id: orderId },
      });
      if (!order) {
        throw new NotFoundException(`Order ${orderId} not found`);
      }

      if (order.orderStatus != OrderStatus.WAIT_PAYMENT) {
        throw new BadRequestException('Order invalid status');
      }

      const paymentMethod = await queryRunner.manager.findOne(PaymentMethod, {
        where: { id: dto.paymentMethodId },
      });
      if (!paymentMethod) {
        throw new NotFoundException(
          `Payment method ${dto.paymentMethodId} not found`,
        );
      }

      const orderPayment = queryRunner.manager.create(OrderPayment, {
        amount: order.grandTotal,
        paymentMethod: paymentMethod,
        remark: dto.remark,
        status: OrderPaymentStatus.SUCCESS,
        order: order,
      });

      await queryRunner.manager.save(orderPayment);

      await queryRunner.manager.update(Order, order.id, {
        orderStatus: OrderStatus.COMPLETE,
        paid: dto.paid,
        change: dto.change,
      });

      await queryRunner.commitTransaction();

      return this.findOne(orderId);
    }
    catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException(err.message);
    } finally {
      await queryRunner.release();
    }
  }

  async createOrderWithPayment(createOrderWithPaymentDto: CreateOrderWithPaymentDto, user: any) {
    const userId = user['sub'];
    // const storeId = user['storeId'];

    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const orderItems = createOrderWithPaymentDto.orderItems.map((itemDto) => {
        return OrderItem.create({
          product: { id: itemDto.productId },
          quantity: itemDto.quantity,
          price: itemDto.price,
          total: itemDto.total,
        });
      });

      const order = Order.create({
        orderNo: await this.orderRepository.generateOrderNum(),
        orderDate: createOrderWithPaymentDto.date,
        total: createOrderWithPaymentDto.total,
        discount: createOrderWithPaymentDto.discount ?? 0,
        orderStatus: OrderStatus.COMPLETE,
        grandTotal: createOrderWithPaymentDto.total - (createOrderWithPaymentDto.discount ?? 0),
        shift: {
          id: createOrderWithPaymentDto.shiftId,
        },
        member: {
          id: createOrderWithPaymentDto.memberId,
        },
        user: {
          id: userId,
        },
        orderItems: orderItems,
        device: {
          id: createOrderWithPaymentDto.deviceId,
        },
        paid: createOrderWithPaymentDto.paid,
        change: createOrderWithPaymentDto.change,
        branch: {
          id: createOrderWithPaymentDto.branchId,
        },
      });

      await queryRunner.manager.save(order);

      const paymentMethod = await queryRunner.manager.findOne(PaymentMethod, {
        where: { id: createOrderWithPaymentDto.paymentMethodId },
      });
      if (!paymentMethod) {
        throw new NotFoundException(
          `Payment method ${createOrderWithPaymentDto.paymentMethodId} not found`,
        );
      }

      const orderPayment = queryRunner.manager.create(OrderPayment, {
        amount: order.grandTotal,
        paymentMethod: paymentMethod,
        remark: createOrderWithPaymentDto.remark,
        status: OrderPaymentStatus.SUCCESS,
        order: order,
      });
      await queryRunner.manager.save(orderPayment);

      // Update inventory
      for (const orderItem of orderItems) {
        const inventory = await queryRunner.manager.findOne(Inventory, {
          where: {
            product: { id: orderItem.product.id },
            branch: { id: order.branch.id },
          },
        });

        if (!inventory) {
          throw new NotFoundException(`Inventory not found for product ${orderItem.product.id}`);
        }

        await this.inventoryService.createTransaction(
          {
            inventoryId: inventory.id,
            type: InventoryTransactionType.STOCK_OUT,
            quantity: orderItem.quantity,
            referenceNo: order.orderNo,
            notes: `Order: ${order.orderNo}`,
          },
          user,
          queryRunner,
        );

        await queryRunner.manager.update(Inventory, inventory.id, {
          currentStock: inventory.currentStock - orderItem.quantity,
          availableStock: inventory.availableStock - orderItem.quantity,
          lastUpdated: new Date(),
        });
      }

      await queryRunner.commitTransaction();

      return this.findOne(order.id);
    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err)
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }

  }
}
